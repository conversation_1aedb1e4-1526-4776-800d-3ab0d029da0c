{"version": 3, "sources": ["../../../../src/client/components/segment-cache-impl/cache.ts"], "sourcesContent": ["import type {\n  TreePrefetch,\n  RootTreePrefetch,\n  SegmentPrefetch,\n} from '../../../server/app-render/collect-segment-data'\nimport type {\n  HeadData,\n  LoadingModuleData,\n} from '../../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  CacheNodeSeedData,\n  Segment as FlightRouterStateSegment,\n} from '../../../server/app-render/types'\nimport {\n  NEXT_DID_POSTPONE_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_ROUTER_STALE_TIME_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_URL,\n  RSC_CONTENT_TYPE_HEADER,\n  RSC_HEADER,\n} from '../app-router-headers'\nimport {\n  createFetch,\n  createFromNextReadableStream,\n  type RequestHeaders,\n} from '../router-reducer/fetch-server-response'\nimport {\n  pingPrefetchTask,\n  type PrefetchTask,\n  type PrefetchSubtaskResult,\n} from './scheduler'\nimport { getAppBuildId } from '../../app-build-id'\nimport { createHrefFromUrl } from '../router-reducer/create-href-from-url'\nimport type {\n  NormalizedHref,\n  NormalizedNextUrl,\n  NormalizedSearch,\n  RouteCacheKey,\n} from './cache-key'\nimport { createTupleMap, type TupleMap, type Prefix } from './tuple-map'\nimport { createLRU } from './lru'\nimport {\n  convertSegmentPathToStaticExportFilename,\n  encodeChildSegmentKey,\n  encodeSegment,\n  ROOT_SEGMENT_KEY,\n} from '../../../shared/lib/segment-cache/segment-value-encoding'\nimport type {\n  FlightRouterState,\n  NavigationFlightResponse,\n} from '../../../server/app-render/types'\nimport { normalizeFlightData } from '../../flight-data-helpers'\nimport { STATIC_STALETIME_MS } from '../router-reducer/prefetch-cache-utils'\nimport { pingVisibleLinks } from '../links'\nimport { PAGE_SEGMENT_KEY } from '../../../shared/lib/segment'\n\n// A note on async/await when working in the prefetch cache:\n//\n// Most async operations in the prefetch cache should *not* use async/await,\n// Instead, spawn a subtask that writes the results to a cache entry, and attach\n// a \"ping\" listener to notify the prefetch queue to try again.\n//\n// The reason is we need to be able to access the segment cache and traverse its\n// data structures synchronously. For example, if there's a synchronous update\n// we can take an immediate snapshot of the cache to produce something we can\n// render. Limiting the use of async/await also makes it easier to avoid race\n// conditions, which is especially important because is cache is mutable.\n//\n// Another reason is that while we're performing async work, it's possible for\n// existing entries to become stale, or for Link prefetches to be removed from\n// the queue. For optimal scheduling, we need to be able to \"cancel\" subtasks\n// that are no longer needed. So, when a segment is received from the server, we\n// restart from the root of the tree that's being prefetched, to confirm all the\n// parent segments are still cached. If the segment is no longer reachable from\n// the root, then it's effectively canceled. This is similar to the design of\n// Rust Futures, or React Suspense.\n\nexport type RouteTree = {\n  key: string\n  segment: FlightRouterStateSegment\n  slots: null | {\n    [parallelRouteKey: string]: RouteTree\n  }\n  isRootLayout: boolean\n}\n\ntype RouteCacheEntryShared = {\n  staleAt: number\n  // This is false only if we're certain the route cannot be intercepted. It's\n  // true in all other cases, including on initialization when we haven't yet\n  // received a response from the server.\n  couldBeIntercepted: boolean\n\n  // LRU-related fields\n  keypath: null | Prefix<RouteCacheKeypath>\n  next: null | RouteCacheEntry\n  prev: null | RouteCacheEntry\n  size: number\n}\n\n/**\n * Tracks the status of a cache entry as it progresses from no data (Empty),\n * waiting for server data (Pending), and finished (either Fulfilled or\n * Rejected depending on the response from the server.\n */\nexport const enum EntryStatus {\n  Empty,\n  Pending,\n  Fulfilled,\n  Rejected,\n}\n\ntype PendingRouteCacheEntry = RouteCacheEntryShared & {\n  status: EntryStatus.Empty | EntryStatus.Pending\n  blockedTasks: Set<PrefetchTask> | null\n  canonicalUrl: null\n  tree: null\n  head: HeadData | null\n  isHeadPartial: true\n  isPPREnabled: false\n}\n\ntype RejectedRouteCacheEntry = RouteCacheEntryShared & {\n  status: EntryStatus.Rejected\n  blockedTasks: Set<PrefetchTask> | null\n  canonicalUrl: null\n  tree: null\n  head: null\n  isHeadPartial: true\n  isPPREnabled: boolean\n}\n\nexport type FulfilledRouteCacheEntry = RouteCacheEntryShared & {\n  status: EntryStatus.Fulfilled\n  blockedTasks: null\n  canonicalUrl: string\n  tree: RouteTree\n  head: HeadData\n  isHeadPartial: boolean\n  isPPREnabled: boolean\n}\n\nexport type RouteCacheEntry =\n  | PendingRouteCacheEntry\n  | FulfilledRouteCacheEntry\n  | RejectedRouteCacheEntry\n\nexport const enum FetchStrategy {\n  PPR,\n  Full,\n  LoadingBoundary,\n}\n\ntype SegmentCacheEntryShared = {\n  staleAt: number\n  fetchStrategy: FetchStrategy\n  revalidating: SegmentCacheEntry | null\n\n  // LRU-related fields\n  keypath: null | Prefix<SegmentCacheKeypath>\n  next: null | SegmentCacheEntry\n  prev: null | SegmentCacheEntry\n  size: number\n}\n\nexport type EmptySegmentCacheEntry = SegmentCacheEntryShared & {\n  status: EntryStatus.Empty\n  rsc: null\n  loading: null\n  isPartial: true\n  promise: null\n}\n\nexport type PendingSegmentCacheEntry = SegmentCacheEntryShared & {\n  status: EntryStatus.Pending\n  rsc: null\n  loading: null\n  isPartial: true\n  promise: null | PromiseWithResolvers<FulfilledSegmentCacheEntry | null>\n}\n\ntype RejectedSegmentCacheEntry = SegmentCacheEntryShared & {\n  status: EntryStatus.Rejected\n  rsc: null\n  loading: null\n  isPartial: true\n  promise: null\n}\n\nexport type FulfilledSegmentCacheEntry = SegmentCacheEntryShared & {\n  status: EntryStatus.Fulfilled\n  rsc: React.ReactNode | null\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n  isPartial: boolean\n  promise: null\n}\n\nexport type SegmentCacheEntry =\n  | EmptySegmentCacheEntry\n  | PendingSegmentCacheEntry\n  | RejectedSegmentCacheEntry\n  | FulfilledSegmentCacheEntry\n\nexport type NonEmptySegmentCacheEntry = Exclude<\n  SegmentCacheEntry,\n  EmptySegmentCacheEntry\n>\n\nconst isOutputExportMode =\n  process.env.NODE_ENV === 'production' &&\n  process.env.__NEXT_CONFIG_OUTPUT === 'export'\n\n// Route cache entries vary on multiple keys: the href and the Next-Url. Each of\n// these parts needs to be included in the internal cache key. Rather than\n// concatenate the keys into a single key, we use a multi-level map, where the\n// first level is keyed by href, the second level is keyed by Next-Url, and so\n// on (if were to add more levels).\ntype RouteCacheKeypath = [NormalizedHref, NormalizedNextUrl]\nlet routeCacheMap: TupleMap<RouteCacheKeypath, RouteCacheEntry> =\n  createTupleMap()\n\n// We use an LRU for memory management. We must update this whenever we add or\n// remove a new cache entry, or when an entry changes size.\n// TODO: I chose the max size somewhat arbitrarily. Consider setting this based\n// on navigator.deviceMemory, or some other heuristic. We should make this\n// customizable via the Next.js config, too.\nconst maxRouteLruSize = 10 * 1024 * 1024 // 10 MB\nlet routeCacheLru = createLRU<RouteCacheEntry>(\n  maxRouteLruSize,\n  onRouteLRUEviction\n)\n\ntype SegmentCacheKeypath = [string, NormalizedSearch]\nlet segmentCacheMap: TupleMap<SegmentCacheKeypath, SegmentCacheEntry> =\n  createTupleMap()\n// NOTE: Segments and Route entries are managed by separate LRUs. We could\n// combine them into a single LRU, but because they are separate types, we'd\n// need to wrap each one in an extra LRU node (to maintain monomorphism, at the\n// cost of additional memory).\nconst maxSegmentLruSize = 50 * 1024 * 1024 // 50 MB\nlet segmentCacheLru = createLRU<SegmentCacheEntry>(\n  maxSegmentLruSize,\n  onSegmentLRUEviction\n)\n\n// Incrementing counter used to track cache invalidations.\nlet currentCacheVersion = 0\n\nexport function getCurrentCacheVersion(): number {\n  return currentCacheVersion\n}\n\n/**\n * Used to clear the client prefetch cache when a server action calls\n * revalidatePath or revalidateTag. Eventually we will support only clearing the\n * segments that were actually affected, but there's more work to be done on the\n * server before the client is able to do this correctly.\n */\nexport function revalidateEntireCache(\n  nextUrl: string | null,\n  tree: FlightRouterState\n) {\n  currentCacheVersion++\n\n  // Clearing the cache also effectively rejects any pending requests, because\n  // when the response is received, it gets written into a cache entry that is\n  // no longer reachable.\n  // TODO: There's an exception to this case that we don't currently handle\n  // correctly: background revalidations. See note in `upsertSegmentEntry`.\n  routeCacheMap = createTupleMap()\n  routeCacheLru = createLRU(maxRouteLruSize, onRouteLRUEviction)\n  segmentCacheMap = createTupleMap()\n  segmentCacheLru = createLRU(maxSegmentLruSize, onSegmentLRUEviction)\n\n  // Prefetch all the currently visible links again, to re-fill the cache.\n  pingVisibleLinks(nextUrl, tree)\n}\n\nexport function readExactRouteCacheEntry(\n  now: number,\n  href: NormalizedHref,\n  nextUrl: NormalizedNextUrl | null\n): RouteCacheEntry | null {\n  const keypath: Prefix<RouteCacheKeypath> =\n    nextUrl === null ? [href] : [href, nextUrl]\n  const existingEntry = routeCacheMap.get(keypath)\n  if (existingEntry !== null) {\n    // Check if the entry is stale\n    if (existingEntry.staleAt > now) {\n      // Reuse the existing entry.\n\n      // Since this is an access, move the entry to the front of the LRU.\n      routeCacheLru.put(existingEntry)\n\n      return existingEntry\n    } else {\n      // Evict the stale entry from the cache.\n      deleteRouteFromCache(existingEntry, keypath)\n    }\n  }\n  return null\n}\n\nexport function readRouteCacheEntry(\n  now: number,\n  key: RouteCacheKey\n): RouteCacheEntry | null {\n  // First check if there's a non-intercepted entry. Most routes cannot be\n  // intercepted, so this is the common case.\n  const nonInterceptedEntry = readExactRouteCacheEntry(now, key.href, null)\n  if (nonInterceptedEntry !== null && !nonInterceptedEntry.couldBeIntercepted) {\n    // Found a match, and the route cannot be intercepted. We can reuse it.\n    return nonInterceptedEntry\n  }\n  // There was no match. Check again but include the Next-Url this time.\n  return readExactRouteCacheEntry(now, key.href, key.nextUrl)\n}\n\nexport function getSegmentKeypathForTask(\n  task: PrefetchTask,\n  route: FulfilledRouteCacheEntry,\n  path: string\n): Prefix<SegmentCacheKeypath> {\n  // When a prefetch includes dynamic data, the search params are included\n  // in the result, so we must include the search string in the segment\n  // cache key. (Note that this is true even if the search string is empty.)\n  //\n  // If we're fetching using PPR, we do not need to include the search params in\n  // the cache key, because the search params are treated as dynamic data. The\n  // cache entry is valid for all possible search param values.\n  const isDynamicTask = task.includeDynamicData || !route.isPPREnabled\n  return isDynamicTask && path.endsWith('/' + PAGE_SEGMENT_KEY)\n    ? [path, task.key.search]\n    : [path]\n}\n\nexport function readSegmentCacheEntry(\n  now: number,\n  routeCacheKey: RouteCacheKey,\n  path: string\n): SegmentCacheEntry | null {\n  if (!path.endsWith('/' + PAGE_SEGMENT_KEY)) {\n    // Fast path. Search params only exist on page segments.\n    return readExactSegmentCacheEntry(now, [path])\n  }\n\n  // Page segments may or may not contain search params. If they were prefetched\n  // using a dynamic request, then we will have an entry with search params.\n  // Check for that case first.\n  const entryWithSearchParams = readExactSegmentCacheEntry(now, [\n    path,\n    routeCacheKey.search,\n  ])\n  if (entryWithSearchParams !== null) {\n    return entryWithSearchParams\n  }\n\n  // If we did not find an entry with the given search params, check for a\n  // \"fallback\" entry, where the search params are treated as dynamic data. This\n  // is the common case because PPR/static prerenders always treat search params\n  // as dynamic.\n  //\n  // See corresponding logic in `getSegmentKeypathForTask`.\n  const entryWithoutSearchParams = readExactSegmentCacheEntry(now, [path])\n  return entryWithoutSearchParams\n}\n\nfunction readExactSegmentCacheEntry(\n  now: number,\n  keypath: Prefix<SegmentCacheKeypath>\n): SegmentCacheEntry | null {\n  const existingEntry = segmentCacheMap.get(keypath)\n  if (existingEntry !== null) {\n    // Check if the entry is stale\n    if (existingEntry.staleAt > now) {\n      // Reuse the existing entry.\n\n      // Since this is an access, move the entry to the front of the LRU.\n      segmentCacheLru.put(existingEntry)\n\n      return existingEntry\n    } else {\n      // This is a stale entry.\n      const revalidatingEntry = existingEntry.revalidating\n      if (revalidatingEntry !== null) {\n        // There's a revalidation in progress. Upsert it.\n        const upsertedEntry = upsertSegmentEntry(\n          now,\n          keypath,\n          revalidatingEntry\n        )\n        if (upsertedEntry !== null && upsertedEntry.staleAt > now) {\n          // We can use the upserted revalidation entry.\n          return upsertedEntry\n        }\n      } else {\n        // Evict the stale entry from the cache.\n        deleteSegmentFromCache(existingEntry, keypath)\n      }\n    }\n  }\n  return null\n}\n\nfunction readRevalidatingSegmentCacheEntry(\n  now: number,\n  owner: SegmentCacheEntry\n): SegmentCacheEntry | null {\n  const existingRevalidation = owner.revalidating\n  if (existingRevalidation !== null) {\n    if (existingRevalidation.staleAt > now) {\n      // There's already a revalidation in progress. Or a previous revalidation\n      // failed and it has not yet expired.\n      return existingRevalidation\n    } else {\n      // Clear the stale revalidation from its owner.\n      clearRevalidatingSegmentFromOwner(owner)\n    }\n  }\n  return null\n}\n\nexport function waitForSegmentCacheEntry(\n  pendingEntry: PendingSegmentCacheEntry\n): Promise<FulfilledSegmentCacheEntry | null> {\n  // Because the entry is pending, there's already a in-progress request.\n  // Attach a promise to the entry that will resolve when the server responds.\n  let promiseWithResolvers = pendingEntry.promise\n  if (promiseWithResolvers === null) {\n    promiseWithResolvers = pendingEntry.promise =\n      createPromiseWithResolvers<FulfilledSegmentCacheEntry | null>()\n  } else {\n    // There's already a promise we can use\n  }\n  return promiseWithResolvers.promise\n}\n\n/**\n * Checks if an entry for a route exists in the cache. If so, it returns the\n * entry, If not, it adds an empty entry to the cache and returns it.\n */\nexport function readOrCreateRouteCacheEntry(\n  now: number,\n  task: PrefetchTask\n): RouteCacheEntry {\n  const key = task.key\n  const existingEntry = readRouteCacheEntry(now, key)\n  if (existingEntry !== null) {\n    return existingEntry\n  }\n  // Create a pending entry and add it to the cache.\n  const pendingEntry: PendingRouteCacheEntry = {\n    canonicalUrl: null,\n    status: EntryStatus.Empty,\n    blockedTasks: null,\n    tree: null,\n    head: null,\n    isHeadPartial: true,\n    // Since this is an empty entry, there's no reason to ever evict it. It will\n    // be updated when the data is populated.\n    staleAt: Infinity,\n    // This is initialized to true because we don't know yet whether the route\n    // could be intercepted. It's only set to false once we receive a response\n    // from the server.\n    couldBeIntercepted: true,\n    // Similarly, we don't yet know if the route supports PPR.\n    isPPREnabled: false,\n\n    // LRU-related fields\n    keypath: null,\n    next: null,\n    prev: null,\n    size: 0,\n  }\n  const keypath: Prefix<RouteCacheKeypath> =\n    key.nextUrl === null ? [key.href] : [key.href, key.nextUrl]\n  routeCacheMap.set(keypath, pendingEntry)\n  // Stash the keypath on the entry so we know how to remove it from the map\n  // if it gets evicted from the LRU.\n  pendingEntry.keypath = keypath\n  routeCacheLru.put(pendingEntry)\n  return pendingEntry\n}\n\n/**\n * Checks if an entry for a segment exists in the cache. If so, it returns the\n * entry, If not, it adds an empty entry to the cache and returns it.\n */\nexport function readOrCreateSegmentCacheEntry(\n  now: number,\n  task: PrefetchTask,\n  route: FulfilledRouteCacheEntry,\n  path: string\n): SegmentCacheEntry {\n  const keypath = getSegmentKeypathForTask(task, route, path)\n  const existingEntry = readExactSegmentCacheEntry(now, keypath)\n  if (existingEntry !== null) {\n    return existingEntry\n  }\n  // Create a pending entry and add it to the cache.\n  const pendingEntry = createDetachedSegmentCacheEntry(route.staleAt)\n  segmentCacheMap.set(keypath, pendingEntry)\n  // Stash the keypath on the entry so we know how to remove it from the map\n  // if it gets evicted from the LRU.\n  pendingEntry.keypath = keypath\n  segmentCacheLru.put(pendingEntry)\n  return pendingEntry\n}\n\nexport function readOrCreateRevalidatingSegmentEntry(\n  now: number,\n  prevEntry: SegmentCacheEntry\n): SegmentCacheEntry {\n  const existingRevalidation = readRevalidatingSegmentCacheEntry(now, prevEntry)\n  if (existingRevalidation !== null) {\n    return existingRevalidation\n  }\n  const pendingEntry = createDetachedSegmentCacheEntry(prevEntry.staleAt)\n\n  // Background revalidations are not stored directly in the cache map or LRU;\n  // they're stashed on the entry that they will (potentially) replace.\n  //\n  // Note that we don't actually ever clear this field, except when the entry\n  // expires. When the revalidation finishes, one of two things will happen:\n  //\n  //  1) the revalidation is successful, `prevEntry` is removed from the cache\n  //     and garbage collected (so there's no point clearing any of its fields)\n  //  2) the revalidation fails, and we'll use the `revalidating` field to\n  //     prevent subsequent revalidation attempts, until it expires.\n  prevEntry.revalidating = pendingEntry\n\n  return pendingEntry\n}\n\nexport function upsertSegmentEntry(\n  now: number,\n  keypath: Prefix<SegmentCacheKeypath>,\n  candidateEntry: SegmentCacheEntry\n): SegmentCacheEntry | null {\n  // We have a new entry that has not yet been inserted into the cache. Before\n  // we do so, we need to confirm whether it takes precedence over the existing\n  // entry (if one exists).\n  // TODO: We should not upsert an entry if its key was invalidated in the time\n  // since the request was made. We can do that by passing the \"owner\" entry to\n  // this function and confirming it's the same as `existingEntry`.\n  const existingEntry = readExactSegmentCacheEntry(now, keypath)\n  if (existingEntry !== null) {\n    if (candidateEntry.isPartial && !existingEntry.isPartial) {\n      // Don't replace a full segment with a partial one. A case where this\n      // might happen is if the existing segment was fetched via\n      // <Link prefetch={true}>.\n\n      // We're going to leave the entry on the owner's `revalidating` field\n      // so that it doesn't get revalidated again unnecessarily. Downgrade the\n      // Fulfilled entry to Rejected and null out the data so it can be garbage\n      // collected. We leave `staleAt` intact to prevent subsequent revalidation\n      // attempts only until the entry expires.\n      const rejectedEntry: RejectedSegmentCacheEntry = candidateEntry as any\n      rejectedEntry.status = EntryStatus.Rejected\n      rejectedEntry.loading = null\n      rejectedEntry.rsc = null\n      return null\n    }\n    // Evict the existing entry from the cache.\n    deleteSegmentFromCache(existingEntry, keypath)\n  }\n  segmentCacheMap.set(keypath, candidateEntry)\n  // Stash the keypath on the entry so we know how to remove it from the map\n  // if it gets evicted from the LRU.\n  candidateEntry.keypath = keypath\n  segmentCacheLru.put(candidateEntry)\n  return candidateEntry\n}\n\nexport function createDetachedSegmentCacheEntry(\n  staleAt: number\n): EmptySegmentCacheEntry {\n  const emptyEntry: EmptySegmentCacheEntry = {\n    status: EntryStatus.Empty,\n    // Default to assuming the fetch strategy will be PPR. This will be updated\n    // when a fetch is actually initiated.\n    fetchStrategy: FetchStrategy.PPR,\n    revalidating: null,\n    rsc: null,\n    loading: null,\n    staleAt,\n    isPartial: true,\n    promise: null,\n\n    // LRU-related fields\n    keypath: null,\n    next: null,\n    prev: null,\n    size: 0,\n  }\n  return emptyEntry\n}\n\nexport function upgradeToPendingSegment(\n  emptyEntry: EmptySegmentCacheEntry,\n  fetchStrategy: FetchStrategy\n): PendingSegmentCacheEntry {\n  const pendingEntry: PendingSegmentCacheEntry = emptyEntry as any\n  pendingEntry.status = EntryStatus.Pending\n  pendingEntry.fetchStrategy = fetchStrategy\n  return pendingEntry\n}\n\nfunction deleteRouteFromCache(\n  entry: RouteCacheEntry,\n  keypath: Prefix<RouteCacheKeypath>\n): void {\n  pingBlockedTasks(entry)\n  routeCacheMap.delete(keypath)\n  routeCacheLru.delete(entry)\n}\n\nfunction deleteSegmentFromCache(\n  entry: SegmentCacheEntry,\n  keypath: Prefix<SegmentCacheKeypath>\n): void {\n  cancelEntryListeners(entry)\n  segmentCacheMap.delete(keypath)\n  segmentCacheLru.delete(entry)\n  clearRevalidatingSegmentFromOwner(entry)\n}\n\nfunction clearRevalidatingSegmentFromOwner(owner: SegmentCacheEntry): void {\n  // Revalidating segments are not stored in the cache directly; they're\n  // stored as a field on the entry that they will (potentially) replace. So\n  // to dispose of an existing revalidation, we just need to null out the field\n  // on the owner.\n  const revalidatingSegment = owner.revalidating\n  if (revalidatingSegment !== null) {\n    cancelEntryListeners(revalidatingSegment)\n    owner.revalidating = null\n  }\n}\n\nexport function resetRevalidatingSegmentEntry(\n  owner: SegmentCacheEntry\n): EmptySegmentCacheEntry {\n  clearRevalidatingSegmentFromOwner(owner)\n  const emptyEntry = createDetachedSegmentCacheEntry(owner.staleAt)\n  owner.revalidating = emptyEntry\n  return emptyEntry\n}\n\nfunction onRouteLRUEviction(entry: RouteCacheEntry): void {\n  // The LRU evicted this entry. Remove it from the map.\n  const keypath = entry.keypath\n  if (keypath !== null) {\n    entry.keypath = null\n    pingBlockedTasks(entry)\n    routeCacheMap.delete(keypath)\n  }\n}\n\nfunction onSegmentLRUEviction(entry: SegmentCacheEntry): void {\n  // The LRU evicted this entry. Remove it from the map.\n  const keypath = entry.keypath\n  if (keypath !== null) {\n    entry.keypath = null\n    cancelEntryListeners(entry)\n    segmentCacheMap.delete(keypath)\n  }\n}\n\nfunction cancelEntryListeners(entry: SegmentCacheEntry): void {\n  if (entry.status === EntryStatus.Pending && entry.promise !== null) {\n    // There were listeners for this entry. Resolve them with `null` to indicate\n    // that the prefetch failed. It's up to the listener to decide how to handle\n    // this case.\n    // NOTE: We don't currently propagate the reason the prefetch was canceled\n    // but we could by accepting a `reason` argument.\n    entry.promise.resolve(null)\n    entry.promise = null\n  }\n}\n\nfunction pingBlockedTasks(entry: {\n  blockedTasks: Set<PrefetchTask> | null\n}): void {\n  const blockedTasks = entry.blockedTasks\n  if (blockedTasks !== null) {\n    for (const task of blockedTasks) {\n      pingPrefetchTask(task)\n    }\n    entry.blockedTasks = null\n  }\n}\n\nfunction fulfillRouteCacheEntry(\n  entry: RouteCacheEntry,\n  tree: RouteTree,\n  head: HeadData,\n  isHeadPartial: boolean,\n  staleAt: number,\n  couldBeIntercepted: boolean,\n  canonicalUrl: string,\n  isPPREnabled: boolean\n): FulfilledRouteCacheEntry {\n  const fulfilledEntry: FulfilledRouteCacheEntry = entry as any\n  fulfilledEntry.status = EntryStatus.Fulfilled\n  fulfilledEntry.tree = tree\n  fulfilledEntry.head = head\n  fulfilledEntry.isHeadPartial = isHeadPartial\n  fulfilledEntry.staleAt = staleAt\n  fulfilledEntry.couldBeIntercepted = couldBeIntercepted\n  fulfilledEntry.canonicalUrl = canonicalUrl\n  fulfilledEntry.isPPREnabled = isPPREnabled\n  pingBlockedTasks(entry)\n  return fulfilledEntry\n}\n\nfunction fulfillSegmentCacheEntry(\n  segmentCacheEntry: EmptySegmentCacheEntry | PendingSegmentCacheEntry,\n  rsc: React.ReactNode,\n  loading: LoadingModuleData | Promise<LoadingModuleData>,\n  staleAt: number,\n  isPartial: boolean\n): FulfilledSegmentCacheEntry {\n  const fulfilledEntry: FulfilledSegmentCacheEntry = segmentCacheEntry as any\n  fulfilledEntry.status = EntryStatus.Fulfilled\n  fulfilledEntry.rsc = rsc\n  fulfilledEntry.loading = loading\n  fulfilledEntry.staleAt = staleAt\n  fulfilledEntry.isPartial = isPartial\n  // Resolve any listeners that were waiting for this data.\n  if (segmentCacheEntry.promise !== null) {\n    segmentCacheEntry.promise.resolve(fulfilledEntry)\n    // Free the promise for garbage collection.\n    fulfilledEntry.promise = null\n  }\n  return fulfilledEntry\n}\n\nfunction rejectRouteCacheEntry(\n  entry: PendingRouteCacheEntry,\n  staleAt: number\n): void {\n  const rejectedEntry: RejectedRouteCacheEntry = entry as any\n  rejectedEntry.status = EntryStatus.Rejected\n  rejectedEntry.staleAt = staleAt\n  pingBlockedTasks(entry)\n}\n\nfunction rejectSegmentCacheEntry(\n  entry: PendingSegmentCacheEntry,\n  staleAt: number\n): void {\n  const rejectedEntry: RejectedSegmentCacheEntry = entry as any\n  rejectedEntry.status = EntryStatus.Rejected\n  rejectedEntry.staleAt = staleAt\n  if (entry.promise !== null) {\n    // NOTE: We don't currently propagate the reason the prefetch was canceled\n    // but we could by accepting a `reason` argument.\n    entry.promise.resolve(null)\n    entry.promise = null\n  }\n}\n\nfunction convertRootTreePrefetchToRouteTree(rootTree: RootTreePrefetch) {\n  return convertTreePrefetchToRouteTree(rootTree.tree, ROOT_SEGMENT_KEY)\n}\n\nfunction convertTreePrefetchToRouteTree(\n  prefetch: TreePrefetch,\n  key: string\n): RouteTree {\n  // Converts the route tree sent by the server into the format used by the\n  // cache. The cached version of the tree includes additional fields, such as a\n  // cache key for each segment. Since this is frequently accessed, we compute\n  // it once instead of on every access. This same cache key is also used to\n  // request the segment from the server.\n  let slots: { [parallelRouteKey: string]: RouteTree } | null = null\n  const prefetchSlots = prefetch.slots\n  if (prefetchSlots !== null) {\n    slots = {}\n    for (let parallelRouteKey in prefetchSlots) {\n      const childPrefetch = prefetchSlots[parallelRouteKey]\n      const childSegment = childPrefetch.segment\n      // TODO: Eventually, the param values will not be included in the response\n      // from the server. We'll instead fill them in on the client by parsing\n      // the URL. This is where we'll do that.\n      const childKey = encodeChildSegmentKey(\n        key,\n        parallelRouteKey,\n        encodeSegment(childSegment)\n      )\n      slots[parallelRouteKey] = convertTreePrefetchToRouteTree(\n        childPrefetch,\n        childKey\n      )\n    }\n  }\n  return {\n    key,\n    segment: prefetch.segment,\n    slots,\n    isRootLayout: prefetch.isRootLayout,\n  }\n}\n\nfunction convertRootFlightRouterStateToRouteTree(\n  flightRouterState: FlightRouterState\n): RouteTree {\n  return convertFlightRouterStateToRouteTree(\n    flightRouterState,\n    ROOT_SEGMENT_KEY\n  )\n}\n\nfunction convertFlightRouterStateToRouteTree(\n  flightRouterState: FlightRouterState,\n  key: string\n): RouteTree {\n  let slots: { [parallelRouteKey: string]: RouteTree } | null = null\n\n  const parallelRoutes = flightRouterState[1]\n  for (let parallelRouteKey in parallelRoutes) {\n    const childRouterState = parallelRoutes[parallelRouteKey]\n    const childSegment = childRouterState[0]\n    // TODO: Eventually, the param values will not be included in the response\n    // from the server. We'll instead fill them in on the client by parsing\n    // the URL. This is where we'll do that.\n    const childKey = encodeChildSegmentKey(\n      key,\n      parallelRouteKey,\n      encodeSegment(childSegment)\n    )\n    const childTree = convertFlightRouterStateToRouteTree(\n      childRouterState,\n      childKey\n    )\n    if (slots === null) {\n      slots = {\n        [parallelRouteKey]: childTree,\n      }\n    } else {\n      slots[parallelRouteKey] = childTree\n    }\n  }\n\n  // The navigation implementation expects the search params to be included\n  // in the segment. However, in the case of a static response, the search\n  // params are omitted. So the client needs to add them back in when reading\n  // from the Segment Cache.\n  //\n  // For consistency, we'll do this for dynamic responses, too.\n  //\n  // TODO: We should move search params out of FlightRouterState and handle them\n  // entirely on the client, similar to our plan for dynamic params.\n  const originalSegment = flightRouterState[0]\n  const segmentWithoutSearchParams =\n    typeof originalSegment === 'string' &&\n    originalSegment.startsWith(PAGE_SEGMENT_KEY)\n      ? PAGE_SEGMENT_KEY\n      : originalSegment\n\n  return {\n    key,\n    segment: segmentWithoutSearchParams,\n    slots,\n    isRootLayout: flightRouterState[4] === true,\n  }\n}\n\nexport function convertRouteTreeToFlightRouterState(\n  routeTree: RouteTree\n): FlightRouterState {\n  const parallelRoutes: Record<string, FlightRouterState> = {}\n  if (routeTree.slots !== null) {\n    for (const parallelRouteKey in routeTree.slots) {\n      parallelRoutes[parallelRouteKey] = convertRouteTreeToFlightRouterState(\n        routeTree.slots[parallelRouteKey]\n      )\n    }\n  }\n  const flightRouterState: FlightRouterState = [\n    routeTree.segment,\n    parallelRoutes,\n    null,\n    null,\n    routeTree.isRootLayout,\n  ]\n  return flightRouterState\n}\n\nexport async function fetchRouteOnCacheMiss(\n  entry: PendingRouteCacheEntry,\n  task: PrefetchTask\n): Promise<PrefetchSubtaskResult<null> | null> {\n  // This function is allowed to use async/await because it contains the actual\n  // fetch that gets issued on a cache miss. Notice it writes the result to the\n  // cache entry directly, rather than return data that is then written by\n  // the caller.\n  const key = task.key\n  const href = key.href\n  const nextUrl = key.nextUrl\n  const segmentPath = '/_tree'\n\n  const headers: RequestHeaders = {\n    [RSC_HEADER]: '1',\n    [NEXT_ROUTER_PREFETCH_HEADER]: '1',\n    [NEXT_ROUTER_SEGMENT_PREFETCH_HEADER]: segmentPath,\n  }\n  if (nextUrl !== null) {\n    headers[NEXT_URL] = nextUrl\n  }\n\n  // In output: \"export\" mode, we need to add the segment path to the URL.\n  const url = new URL(href)\n  const requestUrl = isOutputExportMode\n    ? addSegmentPathToUrlInOutputExportMode(url, segmentPath)\n    : url\n\n  try {\n    const response = await fetchPrefetchResponse(requestUrl, headers)\n    if (\n      !response ||\n      !response.ok ||\n      // 204 is a Cache miss. Though theoretically this shouldn't happen when\n      // PPR is enabled, because we always respond to route tree requests, even\n      // if it needs to be blockingly generated on demand.\n      response.status === 204 ||\n      !response.body\n    ) {\n      // Server responded with an error, or with a miss. We should still cache\n      // the response, but we can try again after 10 seconds.\n      rejectRouteCacheEntry(entry, Date.now() + 10 * 1000)\n      return null\n    }\n\n    // TODO: The canonical URL is the href without the origin. I think\n    // historically the reason for this is because the initial canonical URL\n    // gets passed as a prop to the top-level React component, which means it\n    // needs to be computed during SSR. If it were to include the origin, it\n    // would need to always be same as location.origin on the client, to prevent\n    // a hydration mismatch. To sidestep this complexity, we omit the origin.\n    //\n    // However, since this is neither a native URL object nor a fully qualified\n    // URL string, we need to be careful about how we use it. To prevent subtle\n    // mistakes, we should create a special type for it, instead of just string.\n    // Or, we should just use a (readonly) URL object instead. The type of the\n    // prop that we pass to seed the initial state does not need to be the same\n    // type as the state itself.\n    const canonicalUrl = createHrefFromUrl(\n      new URL(\n        response.redirected\n          ? removeSegmentPathFromURLInOutputExportMode(\n              href,\n              requestUrl.href,\n              response.url\n            )\n          : href\n      )\n    )\n\n    // Check whether the response varies based on the Next-Url header.\n    const varyHeader = response.headers.get('vary')\n    const couldBeIntercepted =\n      varyHeader !== null && varyHeader.includes(NEXT_URL)\n\n    // Track when the network connection closes.\n    const closed = createPromiseWithResolvers<void>()\n\n    // This checks whether the response was served from the per-segment cache,\n    // rather than the old prefetching flow. If it fails, it implies that PPR\n    // is disabled on this route.\n    const routeIsPPREnabled =\n      response.headers.get(NEXT_DID_POSTPONE_HEADER) === '2' ||\n      // In output: \"export\" mode, we can't rely on response headers. But if we\n      // receive a well-formed response, we can assume it's a static response,\n      // because all data is static in this mode.\n      isOutputExportMode\n\n    if (routeIsPPREnabled) {\n      const prefetchStream = createPrefetchResponseStream(\n        response.body,\n        closed.resolve,\n        function onResponseSizeUpdate(size) {\n          routeCacheLru.updateSize(entry, size)\n        }\n      )\n      const serverData = await (createFromNextReadableStream(\n        prefetchStream\n      ) as Promise<RootTreePrefetch>)\n      if (serverData.buildId !== getAppBuildId()) {\n        // The server build does not match the client. Treat as a 404. During\n        // an actual navigation, the router will trigger an MPA navigation.\n        // TODO: Consider moving the build ID to a response header so we can check\n        // it before decoding the response, and so there's one way of checking\n        // across all response types.\n        rejectRouteCacheEntry(entry, Date.now() + 10 * 1000)\n        return null\n      }\n\n      const staleTimeMs = serverData.staleTime * 1000\n      fulfillRouteCacheEntry(\n        entry,\n        convertRootTreePrefetchToRouteTree(serverData),\n        serverData.head,\n        serverData.isHeadPartial,\n        Date.now() + staleTimeMs,\n        couldBeIntercepted,\n        canonicalUrl,\n        routeIsPPREnabled\n      )\n    } else {\n      // PPR is not enabled for this route. The server responds with a\n      // different format (FlightRouterState) that we need to convert.\n      // TODO: We will unify the responses eventually. I'm keeping the types\n      // separate for now because FlightRouterState has so many\n      // overloaded concerns.\n      const prefetchStream = createPrefetchResponseStream(\n        response.body,\n        closed.resolve,\n        function onResponseSizeUpdate(size) {\n          routeCacheLru.updateSize(entry, size)\n        }\n      )\n      const serverData = await (createFromNextReadableStream(\n        prefetchStream\n      ) as Promise<NavigationFlightResponse>)\n\n      writeDynamicTreeResponseIntoCache(\n        Date.now(),\n        task,\n        response,\n        serverData,\n        entry,\n        couldBeIntercepted,\n        canonicalUrl,\n        routeIsPPREnabled\n      )\n    }\n\n    if (!couldBeIntercepted && nextUrl !== null) {\n      // This route will never be intercepted. So we can use this entry for all\n      // requests to this route, regardless of the Next-Url header. This works\n      // because when reading the cache we always check for a valid\n      // non-intercepted entry first.\n      //\n      // Re-key the entry. Since we're in an async task, we must first confirm\n      // that the entry hasn't been concurrently modified by a different task.\n      const currentKeypath: Prefix<RouteCacheKeypath> = [href, nextUrl]\n      const expectedEntry = routeCacheMap.get(currentKeypath)\n      if (expectedEntry === entry) {\n        routeCacheMap.delete(currentKeypath)\n        const newKeypath: Prefix<RouteCacheKeypath> = [href]\n        routeCacheMap.set(newKeypath, entry)\n        // We don't need to update the LRU because the entry is already in it.\n        // But since we changed the keypath, we do need to update that, so we\n        // know how to remove it from the map if it gets evicted from the LRU.\n        entry.keypath = newKeypath\n      } else {\n        // Something else modified this entry already. Since the re-keying is\n        // just a performance optimization, we can safely skip it.\n      }\n    }\n    // Return a promise that resolves when the network connection closes, so\n    // the scheduler can track the number of concurrent network connections.\n    return { value: null, closed: closed.promise }\n  } catch (error) {\n    // Either the connection itself failed, or something bad happened while\n    // decoding the response.\n    rejectRouteCacheEntry(entry, Date.now() + 10 * 1000)\n    return null\n  }\n}\n\nexport async function fetchSegmentOnCacheMiss(\n  route: FulfilledRouteCacheEntry,\n  segmentCacheEntry: PendingSegmentCacheEntry,\n  routeKey: RouteCacheKey,\n  segmentPath: string\n): Promise<PrefetchSubtaskResult<FulfilledSegmentCacheEntry> | null> {\n  // This function is allowed to use async/await because it contains the actual\n  // fetch that gets issued on a cache miss. Notice it writes the result to the\n  // cache entry directly, rather than return data that is then written by\n  // the caller.\n  //\n  // Segment fetches are non-blocking so we don't need to ping the scheduler\n  // on completion.\n\n  // Use the canonical URL to request the segment, not the original URL. These\n  // are usually the same, but the canonical URL will be different if the route\n  // tree response was redirected. To avoid an extra waterfall on every segment\n  // request, we pass the redirected URL instead of the original one.\n  const url = new URL(route.canonicalUrl, routeKey.href)\n  const nextUrl = routeKey.nextUrl\n\n  const normalizedSegmentPath =\n    segmentPath === ROOT_SEGMENT_KEY\n      ? // The root segment is a special case. To simplify the server-side\n        // handling of these requests, we encode the root segment path as\n        // `_index` instead of as an empty string. This should be treated as\n        // an implementation detail and not as a stable part of the protocol.\n        // It just needs to match the equivalent logic that happens when\n        // prerendering the responses. It should not leak outside of Next.js.\n        '/_index'\n      : segmentPath\n\n  const headers: RequestHeaders = {\n    [RSC_HEADER]: '1',\n    [NEXT_ROUTER_PREFETCH_HEADER]: '1',\n    [NEXT_ROUTER_SEGMENT_PREFETCH_HEADER]: normalizedSegmentPath,\n  }\n  if (nextUrl !== null) {\n    headers[NEXT_URL] = nextUrl\n  }\n\n  const requestUrl = isOutputExportMode\n    ? // In output: \"export\" mode, we need to add the segment path to the URL.\n      addSegmentPathToUrlInOutputExportMode(url, normalizedSegmentPath)\n    : url\n  try {\n    const response = await fetchPrefetchResponse(requestUrl, headers)\n    if (\n      !response ||\n      !response.ok ||\n      response.status === 204 || // Cache miss\n      // This checks whether the response was served from the per-segment cache,\n      // rather than the old prefetching flow. If it fails, it implies that PPR\n      // is disabled on this route. Theoretically this should never happen\n      // because we only issue requests for segments once we've verified that\n      // the route supports PPR.\n      (response.headers.get(NEXT_DID_POSTPONE_HEADER) !== '2' &&\n        // In output: \"export\" mode, we can't rely on response headers. But if\n        // we receive a well-formed response, we can assume it's a static\n        // response, because all data is static in this mode.\n        !isOutputExportMode) ||\n      !response.body\n    ) {\n      // Server responded with an error, or with a miss. We should still cache\n      // the response, but we can try again after 10 seconds.\n      rejectSegmentCacheEntry(segmentCacheEntry, Date.now() + 10 * 1000)\n      return null\n    }\n\n    // Track when the network connection closes.\n    const closed = createPromiseWithResolvers<void>()\n\n    // Wrap the original stream in a new stream that never closes. That way the\n    // Flight client doesn't error if there's a hanging promise.\n    const prefetchStream = createPrefetchResponseStream(\n      response.body,\n      closed.resolve,\n      function onResponseSizeUpdate(size) {\n        segmentCacheLru.updateSize(segmentCacheEntry, size)\n      }\n    )\n    const serverData = await (createFromNextReadableStream(\n      prefetchStream\n    ) as Promise<SegmentPrefetch>)\n    if (serverData.buildId !== getAppBuildId()) {\n      // The server build does not match the client. Treat as a 404. During\n      // an actual navigation, the router will trigger an MPA navigation.\n      // TODO: Consider moving the build ID to a response header so we can check\n      // it before decoding the response, and so there's one way of checking\n      // across all response types.\n      rejectSegmentCacheEntry(segmentCacheEntry, Date.now() + 10 * 1000)\n      return null\n    }\n    return {\n      value: fulfillSegmentCacheEntry(\n        segmentCacheEntry,\n        serverData.rsc,\n        serverData.loading,\n        // TODO: The server does not currently provide per-segment stale time.\n        // So we use the stale time of the route.\n        route.staleAt,\n        serverData.isPartial\n      ),\n      // Return a promise that resolves when the network connection closes, so\n      // the scheduler can track the number of concurrent network connections.\n      closed: closed.promise,\n    }\n  } catch (error) {\n    // Either the connection itself failed, or something bad happened while\n    // decoding the response.\n    rejectSegmentCacheEntry(segmentCacheEntry, Date.now() + 10 * 1000)\n    return null\n  }\n}\n\nexport async function fetchSegmentPrefetchesUsingDynamicRequest(\n  task: PrefetchTask,\n  route: FulfilledRouteCacheEntry,\n  fetchStrategy: FetchStrategy,\n  dynamicRequestTree: FlightRouterState,\n  spawnedEntries: Map<string, PendingSegmentCacheEntry>\n): Promise<PrefetchSubtaskResult<null> | null> {\n  const url = new URL(route.canonicalUrl, task.key.href)\n  const nextUrl = task.key.nextUrl\n  const headers: RequestHeaders = {\n    [RSC_HEADER]: '1',\n    [NEXT_ROUTER_STATE_TREE_HEADER]: encodeURIComponent(\n      JSON.stringify(dynamicRequestTree)\n    ),\n  }\n  if (nextUrl !== null) {\n    headers[NEXT_URL] = nextUrl\n  }\n  // Only set the prefetch header if we're not doing a \"full\" prefetch. We\n  // omit the prefetch header from a full prefetch because it's essentially\n  // just a navigation request that happens ahead of time — it should include\n  // all the same data in the response.\n  if (fetchStrategy !== FetchStrategy.Full) {\n    headers[NEXT_ROUTER_PREFETCH_HEADER] = '1'\n  }\n  try {\n    const response = await fetchPrefetchResponse(url, headers)\n    if (!response || !response.ok || !response.body) {\n      // Server responded with an error, or with a miss. We should still cache\n      // the response, but we can try again after 10 seconds.\n      rejectSegmentEntriesIfStillPending(spawnedEntries, Date.now() + 10 * 1000)\n      return null\n    }\n\n    // Track when the network connection closes.\n    const closed = createPromiseWithResolvers<void>()\n\n    let fulfilledEntries: Array<FulfilledSegmentCacheEntry> | null = null\n    const prefetchStream = createPrefetchResponseStream(\n      response.body,\n      closed.resolve,\n      function onResponseSizeUpdate(totalBytesReceivedSoFar) {\n        // When processing a dynamic response, we don't know how large each\n        // individual segment is, so approximate by assiging each segment\n        // the average of the total response size.\n        if (fulfilledEntries === null) {\n          // Haven't received enough data yet to know which segments\n          // were included.\n          return\n        }\n        const averageSize = totalBytesReceivedSoFar / fulfilledEntries.length\n        for (const entry of fulfilledEntries) {\n          segmentCacheLru.updateSize(entry, averageSize)\n        }\n      }\n    )\n    const serverData = await (createFromNextReadableStream(\n      prefetchStream\n    ) as Promise<NavigationFlightResponse>)\n\n    // Since we did not set the prefetch header, the response from the server\n    // will never contain dynamic holes.\n    const isResponsePartial = false\n\n    // Aside from writing the data into the cache, this function also returns\n    // the entries that were fulfilled, so we can streamingly update their sizes\n    // in the LRU as more data comes in.\n    fulfilledEntries = writeDynamicRenderResponseIntoCache(\n      Date.now(),\n      task,\n      response,\n      serverData,\n      isResponsePartial,\n      route,\n      spawnedEntries\n    )\n\n    // Return a promise that resolves when the network connection closes, so\n    // the scheduler can track the number of concurrent network connections.\n    return { value: null, closed: closed.promise }\n  } catch (error) {\n    rejectSegmentEntriesIfStillPending(spawnedEntries, Date.now() + 10 * 1000)\n    return null\n  }\n}\n\nfunction writeDynamicTreeResponseIntoCache(\n  now: number,\n  task: PrefetchTask,\n  response: Response,\n  serverData: NavigationFlightResponse,\n  entry: PendingRouteCacheEntry,\n  couldBeIntercepted: boolean,\n  canonicalUrl: string,\n  routeIsPPREnabled: boolean\n) {\n  if (serverData.b !== getAppBuildId()) {\n    // The server build does not match the client. Treat as a 404. During\n    // an actual navigation, the router will trigger an MPA navigation.\n    // TODO: Consider moving the build ID to a response header so we can check\n    // it before decoding the response, and so there's one way of checking\n    // across all response types.\n    rejectRouteCacheEntry(entry, now + 10 * 1000)\n    return\n  }\n  const normalizedFlightDataResult = normalizeFlightData(serverData.f)\n  if (\n    // A string result means navigating to this route will result in an\n    // MPA navigation.\n    typeof normalizedFlightDataResult === 'string' ||\n    normalizedFlightDataResult.length !== 1\n  ) {\n    rejectRouteCacheEntry(entry, now + 10 * 1000)\n    return\n  }\n  const flightData = normalizedFlightDataResult[0]\n  if (!flightData.isRootRender) {\n    // Unexpected response format.\n    rejectRouteCacheEntry(entry, now + 10 * 1000)\n    return\n  }\n\n  const flightRouterState = flightData.tree\n  // TODO: Extract to function\n  const staleTimeHeaderSeconds = response.headers.get(\n    NEXT_ROUTER_STALE_TIME_HEADER\n  )\n  const staleTimeMs =\n    staleTimeHeaderSeconds !== null\n      ? parseInt(staleTimeHeaderSeconds, 10) * 1000\n      : STATIC_STALETIME_MS\n\n  // If the response contains dynamic holes, then we must conservatively assume\n  // that any individual segment might contain dynamic holes, and also the\n  // head. If it did not contain dynamic holes, then we can assume every segment\n  // and the head is completely static.\n  const isResponsePartial =\n    response.headers.get(NEXT_DID_POSTPONE_HEADER) === '1'\n\n  const fulfilledEntry = fulfillRouteCacheEntry(\n    entry,\n    convertRootFlightRouterStateToRouteTree(flightRouterState),\n    flightData.head,\n    isResponsePartial,\n    now + staleTimeMs,\n    couldBeIntercepted,\n    canonicalUrl,\n    routeIsPPREnabled\n  )\n\n  // If the server sent segment data as part of the response, we should write\n  // it into the cache to prevent a second, redundant prefetch request.\n  //\n  // TODO: When `clientSegmentCache` is enabled, the server does not include\n  // segment data when responding to a route tree prefetch request. However,\n  // when `clientSegmentCache` is set to \"client-only\", and PPR is enabled (or\n  // the page is fully static), the normal check is bypassed and the server\n  // responds with the full page. This is a temporary situation until we can\n  // remove the \"client-only\" option. Then, we can delete this function call.\n  writeDynamicRenderResponseIntoCache(\n    now,\n    task,\n    response,\n    serverData,\n    isResponsePartial,\n    fulfilledEntry,\n    null\n  )\n}\n\nfunction rejectSegmentEntriesIfStillPending(\n  entries: Map<string, SegmentCacheEntry>,\n  staleAt: number\n): Array<FulfilledSegmentCacheEntry> {\n  const fulfilledEntries = []\n  for (const entry of entries.values()) {\n    if (entry.status === EntryStatus.Pending) {\n      rejectSegmentCacheEntry(entry, staleAt)\n    } else if (entry.status === EntryStatus.Fulfilled) {\n      fulfilledEntries.push(entry)\n    }\n  }\n  return fulfilledEntries\n}\n\nfunction writeDynamicRenderResponseIntoCache(\n  now: number,\n  task: PrefetchTask,\n  response: Response,\n  serverData: NavigationFlightResponse,\n  isResponsePartial: boolean,\n  route: FulfilledRouteCacheEntry,\n  spawnedEntries: Map<string, PendingSegmentCacheEntry> | null\n): Array<FulfilledSegmentCacheEntry> | null {\n  if (serverData.b !== getAppBuildId()) {\n    // The server build does not match the client. Treat as a 404. During\n    // an actual navigation, the router will trigger an MPA navigation.\n    // TODO: Consider moving the build ID to a response header so we can check\n    // it before decoding the response, and so there's one way of checking\n    // across all response types.\n    if (spawnedEntries !== null) {\n      rejectSegmentEntriesIfStillPending(spawnedEntries, now + 10 * 1000)\n    }\n    return null\n  }\n  const flightDatas = normalizeFlightData(serverData.f)\n  if (typeof flightDatas === 'string') {\n    // This means navigating to this route will result in an MPA navigation.\n    // TODO: We should cache this, too, so that the MPA navigation is immediate.\n    return null\n  }\n  for (const flightData of flightDatas) {\n    const seedData = flightData.seedData\n    if (seedData !== null) {\n      // The data sent by the server represents only a subtree of the app. We\n      // need to find the part of the task tree that matches the response.\n      //\n      // segmentPath represents the parent path of subtree. It's a repeating\n      // pattern of parallel route key and segment:\n      //\n      //   [string, Segment, string, Segment, string, Segment, ...]\n      const segmentPath = flightData.segmentPath\n      let segmentKey = ROOT_SEGMENT_KEY\n      for (let i = 0; i < segmentPath.length; i += 2) {\n        const parallelRouteKey: string = segmentPath[i]\n        const segment: FlightRouterStateSegment = segmentPath[i + 1]\n        segmentKey = encodeChildSegmentKey(\n          segmentKey,\n          parallelRouteKey,\n          encodeSegment(segment)\n        )\n      }\n      const staleTimeHeaderSeconds = response.headers.get(\n        NEXT_ROUTER_STALE_TIME_HEADER\n      )\n      const staleTimeMs =\n        staleTimeHeaderSeconds !== null\n          ? parseInt(staleTimeHeaderSeconds, 10) * 1000\n          : STATIC_STALETIME_MS\n      writeSeedDataIntoCache(\n        now,\n        task,\n        route,\n        now + staleTimeMs,\n        seedData,\n        isResponsePartial,\n        segmentKey,\n        spawnedEntries\n      )\n    }\n  }\n  // Any entry that's still pending was intentionally not rendered by the\n  // server, because it was inside the loading boundary. Mark them as rejected\n  // so we know not to fetch them again.\n  // TODO: If PPR is enabled on some routes but not others, then it's possible\n  // that a different page is able to do a per-segment prefetch of one of the\n  // segments we're marking as rejected here. We should mark on the segment\n  // somehow that the reason for the rejection is because of a non-PPR prefetch.\n  // That way a per-segment prefetch knows to disregard the rejection.\n  if (spawnedEntries !== null) {\n    const fulfilledEntries = rejectSegmentEntriesIfStillPending(\n      spawnedEntries,\n      now + 10 * 1000\n    )\n    return fulfilledEntries\n  }\n  return null\n}\n\nfunction writeSeedDataIntoCache(\n  now: number,\n  task: PrefetchTask,\n  route: FulfilledRouteCacheEntry,\n  staleAt: number,\n  seedData: CacheNodeSeedData,\n  isResponsePartial: boolean,\n  key: string,\n  entriesOwnedByCurrentTask: Map<string, PendingSegmentCacheEntry> | null\n) {\n  // This function is used to write the result of a dynamic server request\n  // (CacheNodeSeedData) into the prefetch cache. It's used in cases where we\n  // want to treat a dynamic response as if it were static. The two examples\n  // where this happens are <Link prefetch={true}> (which implicitly opts\n  // dynamic data into being static) and when prefetching a PPR-disabled route\n  const rsc = seedData[1]\n  const loading = seedData[3]\n  const isPartial = rsc === null || isResponsePartial\n\n  // We should only write into cache entries that are owned by us. Or create\n  // a new one and write into that. We must never write over an entry that was\n  // created by a different task, because that causes data races.\n  const ownedEntry =\n    entriesOwnedByCurrentTask !== null\n      ? entriesOwnedByCurrentTask.get(key)\n      : undefined\n  if (ownedEntry !== undefined) {\n    fulfillSegmentCacheEntry(ownedEntry, rsc, loading, staleAt, isPartial)\n  } else {\n    // There's no matching entry. Attempt to create a new one.\n    const possiblyNewEntry = readOrCreateSegmentCacheEntry(\n      now,\n      task,\n      route,\n      key\n    )\n    if (possiblyNewEntry.status === EntryStatus.Empty) {\n      // Confirmed this is a new entry. We can fulfill it.\n      const newEntry = possiblyNewEntry\n      fulfillSegmentCacheEntry(newEntry, rsc, loading, staleAt, isPartial)\n    } else {\n      // There was already an entry in the cache. But we may be able to\n      // replace it with the new one from the server.\n      const newEntry = fulfillSegmentCacheEntry(\n        createDetachedSegmentCacheEntry(staleAt),\n        rsc,\n        loading,\n        staleAt,\n        isPartial\n      )\n      upsertSegmentEntry(\n        now,\n        getSegmentKeypathForTask(task, route, key),\n        newEntry\n      )\n    }\n  }\n  // Recursively write the child data into the cache.\n  const seedDataChildren = seedData[2]\n  if (seedDataChildren !== null) {\n    for (const parallelRouteKey in seedDataChildren) {\n      const childSeedData = seedDataChildren[parallelRouteKey]\n      if (childSeedData !== null) {\n        const childSegment = childSeedData[0]\n        writeSeedDataIntoCache(\n          now,\n          task,\n          route,\n          staleAt,\n          childSeedData,\n          isResponsePartial,\n          encodeChildSegmentKey(\n            key,\n            parallelRouteKey,\n            encodeSegment(childSegment)\n          ),\n          entriesOwnedByCurrentTask\n        )\n      }\n    }\n  }\n}\n\nasync function fetchPrefetchResponse(\n  url: URL,\n  headers: RequestHeaders\n): Promise<Response | null> {\n  const fetchPriority = 'low'\n  const response = await createFetch(url, headers, fetchPriority)\n  if (!response.ok) {\n    return null\n  }\n\n  // Check the content type\n  if (isOutputExportMode) {\n    // In output: \"export\" mode, we relaxed about the content type, since it's\n    // not Next.js that's serving the response. If the status is OK, assume the\n    // response is valid. If it's not a valid response, the Flight client won't\n    // be able to decode it, and we'll treat it as a miss.\n  } else {\n    const contentType = response.headers.get('content-type')\n    const isFlightResponse =\n      contentType && contentType.startsWith(RSC_CONTENT_TYPE_HEADER)\n    if (!isFlightResponse) {\n      return null\n    }\n  }\n  return response\n}\n\nfunction createPrefetchResponseStream(\n  originalFlightStream: ReadableStream<Uint8Array>,\n  onStreamClose: () => void,\n  onResponseSizeUpdate: (size: number) => void\n): ReadableStream<Uint8Array> {\n  // When PPR is enabled, prefetch streams may contain references that never\n  // resolve, because that's how we encode dynamic data access. In the decoded\n  // object returned by the Flight client, these are reified into hanging\n  // promises that suspend during render, which is effectively what we want.\n  // The UI resolves when it switches to the dynamic data stream\n  // (via useDeferredValue(dynamic, static)).\n  //\n  // However, the Flight implementation currently errors if the server closes\n  // the response before all the references are resolved. As a cheat to work\n  // around this, we wrap the original stream in a new stream that never closes,\n  // and therefore doesn't error.\n  //\n  // While processing the original stream, we also incrementally update the size\n  // of the cache entry in the LRU.\n  let totalByteLength = 0\n  const reader = originalFlightStream.getReader()\n  return new ReadableStream({\n    async pull(controller) {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (!done) {\n          // Pass to the target stream and keep consuming the Flight response\n          // from the server.\n          controller.enqueue(value)\n\n          // Incrementally update the size of the cache entry in the LRU.\n          // NOTE: Since prefetch responses are delivered in a single chunk,\n          // it's not really necessary to do this streamingly, but I'm doing it\n          // anyway in case this changes in the future.\n          totalByteLength += value.byteLength\n          onResponseSizeUpdate(totalByteLength)\n          continue\n        }\n        // The server stream has closed. Exit, but intentionally do not close\n        // the target stream. We do notify the caller, though.\n        onStreamClose()\n        return\n      }\n    },\n  })\n}\n\nfunction addSegmentPathToUrlInOutputExportMode(\n  url: URL,\n  segmentPath: string\n): URL {\n  if (isOutputExportMode) {\n    // In output: \"export\" mode, we cannot use a header to encode the segment\n    // path. Instead, we append it to the end of the pathname.\n    const staticUrl = new URL(url)\n    const routeDir = staticUrl.pathname.endsWith('/')\n      ? staticUrl.pathname.substring(0, -1)\n      : staticUrl.pathname\n    const staticExportFilename =\n      convertSegmentPathToStaticExportFilename(segmentPath)\n    staticUrl.pathname = `${routeDir}/${staticExportFilename}`\n    return staticUrl\n  }\n  return url\n}\n\nfunction removeSegmentPathFromURLInOutputExportMode(\n  href: string,\n  requestUrl: string,\n  redirectUrl: string\n) {\n  if (isOutputExportMode) {\n    // Reverse of addSegmentPathToUrlInOutputExportMode.\n    //\n    // In output: \"export\" mode, we append an extra string to the URL that\n    // represents the segment path. If the server performs a redirect, it must\n    // include the segment path in new URL.\n    //\n    // This removes the segment path from the redirected URL to obtain the\n    // URL of the page.\n    const segmentPath = requestUrl.substring(href.length)\n    if (redirectUrl.endsWith(segmentPath)) {\n      // Remove the segment path from the redirect URL to get the page URL.\n      return redirectUrl.substring(0, redirectUrl.length - segmentPath.length)\n    } else {\n      // The server redirected to a URL that doesn't include the segment path.\n      // This suggests the server may not have been configured correctly, but\n      // we'll assume the redirected URL represents the page URL and continue.\n      // TODO: Consider printing a warning with a link to a page that explains\n      // how to configure redirects and rewrites correctly.\n    }\n  }\n  return redirectUrl\n}\n\nfunction createPromiseWithResolvers<T>(): PromiseWithResolvers<T> {\n  // Shim of Stage 4 Promise.withResolvers proposal\n  let resolve: (value: T | PromiseLike<T>) => void\n  let reject: (reason: any) => void\n  const promise = new Promise<T>((res, rej) => {\n    resolve = res\n    reject = rej\n  })\n  return { resolve: resolve!, reject: reject!, promise }\n}\n"], "names": ["NEXT_DID_POSTPONE_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_ROUTER_STALE_TIME_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "RSC_HEADER", "createFetch", "createFromNextReadableStream", "pingPrefetchTask", "getAppBuildId", "createHrefFromUrl", "createTupleMap", "createLRU", "convertSegmentPathToStaticExportFilename", "encodeChildSegmentKey", "encodeSegment", "ROOT_SEGMENT_KEY", "normalizeFlightData", "STATIC_STALETIME_MS", "pingVisibleLinks", "PAGE_SEGMENT_KEY", "EntryStatus", "FetchStrategy", "isOutputExportMode", "process", "env", "NODE_ENV", "__NEXT_CONFIG_OUTPUT", "routeCacheMap", "maxRouteLruSize", "routeCacheLru", "onRouteLRUEviction", "segmentCacheMap", "maxSegmentLruSize", "segmentCacheLru", "onSegmentLRUEviction", "currentCacheVersion", "getCurrentCacheVersion", "revalidateEntireCache", "nextUrl", "tree", "readExactRouteCacheEntry", "now", "href", "keypath", "existingEntry", "get", "staleAt", "put", "deleteRouteFromCache", "readRouteCacheEntry", "key", "nonInterceptedEntry", "couldBeIntercepted", "getSegmentKeypathForTask", "task", "route", "path", "isDynamicTask", "includeDynamicData", "isPPREnabled", "endsWith", "search", "readSegmentCacheEntry", "routeCacheKey", "readExactSegmentCacheEntry", "entryWithSearchParams", "entryWithoutSearchParams", "revalidatingEntry", "revalidating", "upsertedEntry", "upsertSegmentEntry", "deleteSegmentFromCache", "readRevalidatingSegmentCacheEntry", "owner", "existingRevalidation", "clearRevalidatingSegmentFromOwner", "waitForSegmentCacheEntry", "pendingEntry", "promiseWithResolvers", "promise", "createPromiseWithResolvers", "readOrCreateRouteCacheEntry", "canonicalUrl", "status", "blockedTasks", "head", "isHeadPartial", "Infinity", "next", "prev", "size", "set", "readOrCreateSegmentCacheEntry", "createDetachedSegmentCacheEntry", "readOrCreateRevalidatingSegmentEntry", "prevEntry", "candidate<PERSON><PERSON><PERSON>", "isPartial", "rejectedEntry", "loading", "rsc", "emptyEntry", "fetchStrategy", "upgradeToPendingSegment", "entry", "pingBlockedTasks", "delete", "cancelEntryListeners", "revalidatingSegment", "resetRevalidatingSegmentEntry", "resolve", "fulfillRouteCacheEntry", "fulfilledEntry", "fulfillSegmentCacheEntry", "segmentCacheEntry", "rejectRouteCacheEntry", "rejectSegmentCacheEntry", "convertRootTreePrefetchToRouteTree", "rootTree", "convertTreePrefetchToRouteTree", "prefetch", "slots", "prefetchSlots", "parallelRouteKey", "childPrefetch", "childSegment", "segment", "<PERSON><PERSON><PERSON>", "isRootLayout", "convertRootFlightRouterStateToRouteTree", "flightRouterState", "convertFlightRouterStateToRouteTree", "parallelRoutes", "childRouterState", "childTree", "originalSegment", "segmentWithoutSearchParams", "startsWith", "convertRouteTreeToFlightRouterState", "routeTree", "fetchRouteOnCacheMiss", "segmentPath", "headers", "url", "URL", "requestUrl", "addSegmentPathToUrlInOutputExportMode", "response", "fetchPrefetchResponse", "ok", "body", "Date", "redirected", "removeSegmentPathFromURLInOutputExportMode", "<PERSON><PERSON><PERSON><PERSON>", "includes", "closed", "routeIsPPREnabled", "prefetchStream", "createPrefetchResponseStream", "onResponseSizeUpdate", "updateSize", "serverData", "buildId", "staleTimeMs", "staleTime", "writeDynamicTreeResponseIntoCache", "currentKeypath", "expectedEntry", "newKeypath", "value", "error", "fetchSegmentOnCacheMiss", "routeKey", "normalizedSegmentPath", "fetchSegmentPrefetchesUsingDynamicRequest", "dynamicRequestTree", "spawnedEntries", "encodeURIComponent", "JSON", "stringify", "rejectSegmentEntriesIfStillPending", "fulfilledEntries", "totalBytesReceivedSoFar", "averageSize", "length", "isResponsePartial", "writeDynamicRenderResponseIntoCache", "b", "normalizedFlightDataResult", "f", "flightData", "isRootRender", "staleTimeHeaderSeconds", "parseInt", "entries", "values", "push", "flightDatas", "seedData", "segmentKey", "i", "writeSeedDataIntoCache", "entriesOwnedByCurrentTask", "ownedEntry", "undefined", "possiblyNewEntry", "newEntry", "seedDataChildren", "childSeedData", "fetchPriority", "contentType", "isFlightResponse", "originalFlightStream", "onStreamClose", "totalByteLength", "reader", "<PERSON><PERSON><PERSON><PERSON>", "ReadableStream", "pull", "controller", "done", "read", "enqueue", "byteLength", "staticUrl", "routeDir", "pathname", "substring", "staticExportFilename", "redirectUrl", "reject", "Promise", "res", "rej"], "mappings": "AAaA,SACEA,wBAAwB,EACxBC,2BAA2B,EAC3BC,mCAAmC,EACnCC,6BAA6B,EAC7BC,6BAA6B,EAC7BC,QAAQ,EACRC,uBAAuB,EACvBC,UAAU,QACL,wBAAuB;AAC9B,SACEC,WAAW,EACXC,4BAA4B,QAEvB,0CAAyC;AAChD,SACEC,gBAAgB,QAGX,cAAa;AACpB,SAASC,aAAa,QAAQ,qBAAoB;AAClD,SAASC,iBAAiB,QAAQ,yCAAwC;AAO1E,SAASC,cAAc,QAAoC,cAAa;AACxE,SAASC,SAAS,QAAQ,QAAO;AACjC,SACEC,wCAAwC,EACxCC,qBAAqB,EACrBC,aAAa,EACbC,gBAAgB,QACX,2DAA0D;AAKjE,SAASC,mBAAmB,QAAQ,4BAA2B;AAC/D,SAASC,mBAAmB,QAAQ,yCAAwC;AAC5E,SAASC,gBAAgB,QAAQ,WAAU;AAC3C,SAASC,gBAAgB,QAAQ,8BAA6B;AA8C9D;;;;CAIC,GACD,OAAO,IAAA,AAAWC,qCAAAA;;;;;WAAAA;MAKjB;AAqCD,OAAO,IAAA,AAAWC,uCAAAA;;;;WAAAA;MAIjB;AAyDD,MAAMC,qBACJC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACzBF,QAAQC,GAAG,CAACE,oBAAoB,KAAK;AAQvC,IAAIC,gBACFjB;AAEF,8EAA8E;AAC9E,2DAA2D;AAC3D,+EAA+E;AAC/E,0EAA0E;AAC1E,4CAA4C;AAC5C,MAAMkB,kBAAkB,KAAK,OAAO,KAAK,QAAQ;;AACjD,IAAIC,gBAAgBlB,UAClBiB,iBACAE;AAIF,IAAIC,kBACFrB;AACF,0EAA0E;AAC1E,4EAA4E;AAC5E,+EAA+E;AAC/E,8BAA8B;AAC9B,MAAMsB,oBAAoB,KAAK,OAAO,KAAK,QAAQ;;AACnD,IAAIC,kBAAkBtB,UACpBqB,mBACAE;AAGF,0DAA0D;AAC1D,IAAIC,sBAAsB;AAE1B,OAAO,SAASC;IACd,OAAOD;AACT;AAEA;;;;;CAKC,GACD,OAAO,SAASE,sBACdC,OAAsB,EACtBC,IAAuB;IAEvBJ;IAEA,4EAA4E;IAC5E,4EAA4E;IAC5E,uBAAuB;IACvB,yEAAyE;IACzE,yEAAyE;IACzER,gBAAgBjB;IAChBmB,gBAAgBlB,UAAUiB,iBAAiBE;IAC3CC,kBAAkBrB;IAClBuB,kBAAkBtB,UAAUqB,mBAAmBE;IAE/C,wEAAwE;IACxEhB,iBAAiBoB,SAASC;AAC5B;AAEA,OAAO,SAASC,yBACdC,GAAW,EACXC,IAAoB,EACpBJ,OAAiC;IAEjC,MAAMK,UACJL,YAAY,OAAO;QAACI;KAAK,GAAG;QAACA;QAAMJ;KAAQ;IAC7C,MAAMM,gBAAgBjB,cAAckB,GAAG,CAACF;IACxC,IAAIC,kBAAkB,MAAM;QAC1B,8BAA8B;QAC9B,IAAIA,cAAcE,OAAO,GAAGL,KAAK;YAC/B,4BAA4B;YAE5B,mEAAmE;YACnEZ,cAAckB,GAAG,CAACH;YAElB,OAAOA;QACT,OAAO;YACL,wCAAwC;YACxCI,qBAAqBJ,eAAeD;QACtC;IACF;IACA,OAAO;AACT;AAEA,OAAO,SAASM,oBACdR,GAAW,EACXS,GAAkB;IAElB,wEAAwE;IACxE,2CAA2C;IAC3C,MAAMC,sBAAsBX,yBAAyBC,KAAKS,IAAIR,IAAI,EAAE;IACpE,IAAIS,wBAAwB,QAAQ,CAACA,oBAAoBC,kBAAkB,EAAE;QAC3E,uEAAuE;QACvE,OAAOD;IACT;IACA,sEAAsE;IACtE,OAAOX,yBAAyBC,KAAKS,IAAIR,IAAI,EAAEQ,IAAIZ,OAAO;AAC5D;AAEA,OAAO,SAASe,yBACdC,IAAkB,EAClBC,KAA+B,EAC/BC,IAAY;IAEZ,wEAAwE;IACxE,qEAAqE;IACrE,0EAA0E;IAC1E,EAAE;IACF,8EAA8E;IAC9E,4EAA4E;IAC5E,6DAA6D;IAC7D,MAAMC,gBAAgBH,KAAKI,kBAAkB,IAAI,CAACH,MAAMI,YAAY;IACpE,OAAOF,iBAAiBD,KAAKI,QAAQ,CAAC,MAAMzC,oBACxC;QAACqC;QAAMF,KAAKJ,GAAG,CAACW,MAAM;KAAC,GACvB;QAACL;KAAK;AACZ;AAEA,OAAO,SAASM,sBACdrB,GAAW,EACXsB,aAA4B,EAC5BP,IAAY;IAEZ,IAAI,CAACA,KAAKI,QAAQ,CAAC,MAAMzC,mBAAmB;QAC1C,wDAAwD;QACxD,OAAO6C,2BAA2BvB,KAAK;YAACe;SAAK;IAC/C;IAEA,8EAA8E;IAC9E,0EAA0E;IAC1E,6BAA6B;IAC7B,MAAMS,wBAAwBD,2BAA2BvB,KAAK;QAC5De;QACAO,cAAcF,MAAM;KACrB;IACD,IAAII,0BAA0B,MAAM;QAClC,OAAOA;IACT;IAEA,wEAAwE;IACxE,8EAA8E;IAC9E,8EAA8E;IAC9E,cAAc;IACd,EAAE;IACF,yDAAyD;IACzD,MAAMC,2BAA2BF,2BAA2BvB,KAAK;QAACe;KAAK;IACvE,OAAOU;AACT;AAEA,SAASF,2BACPvB,GAAW,EACXE,OAAoC;IAEpC,MAAMC,gBAAgBb,gBAAgBc,GAAG,CAACF;IAC1C,IAAIC,kBAAkB,MAAM;QAC1B,8BAA8B;QAC9B,IAAIA,cAAcE,OAAO,GAAGL,KAAK;YAC/B,4BAA4B;YAE5B,mEAAmE;YACnER,gBAAgBc,GAAG,CAACH;YAEpB,OAAOA;QACT,OAAO;YACL,yBAAyB;YACzB,MAAMuB,oBAAoBvB,cAAcwB,YAAY;YACpD,IAAID,sBAAsB,MAAM;gBAC9B,iDAAiD;gBACjD,MAAME,gBAAgBC,mBACpB7B,KACAE,SACAwB;gBAEF,IAAIE,kBAAkB,QAAQA,cAAcvB,OAAO,GAAGL,KAAK;oBACzD,8CAA8C;oBAC9C,OAAO4B;gBACT;YACF,OAAO;gBACL,wCAAwC;gBACxCE,uBAAuB3B,eAAeD;YACxC;QACF;IACF;IACA,OAAO;AACT;AAEA,SAAS6B,kCACP/B,GAAW,EACXgC,KAAwB;IAExB,MAAMC,uBAAuBD,MAAML,YAAY;IAC/C,IAAIM,yBAAyB,MAAM;QACjC,IAAIA,qBAAqB5B,OAAO,GAAGL,KAAK;YACtC,yEAAyE;YACzE,qCAAqC;YACrC,OAAOiC;QACT,OAAO;YACL,+CAA+C;YAC/CC,kCAAkCF;QACpC;IACF;IACA,OAAO;AACT;AAEA,OAAO,SAASG,yBACdC,YAAsC;IAEtC,uEAAuE;IACvE,4EAA4E;IAC5E,IAAIC,uBAAuBD,aAAaE,OAAO;IAC/C,IAAID,yBAAyB,MAAM;QACjCA,uBAAuBD,aAAaE,OAAO,GACzCC;IACJ,OAAO;IACL,uCAAuC;IACzC;IACA,OAAOF,qBAAqBC,OAAO;AACrC;AAEA;;;CAGC,GACD,OAAO,SAASE,4BACdxC,GAAW,EACXa,IAAkB;IAElB,MAAMJ,MAAMI,KAAKJ,GAAG;IACpB,MAAMN,gBAAgBK,oBAAoBR,KAAKS;IAC/C,IAAIN,kBAAkB,MAAM;QAC1B,OAAOA;IACT;IACA,kDAAkD;IAClD,MAAMiC,eAAuC;QAC3CK,cAAc;QACdC,MAAM;QACNC,cAAc;QACd7C,MAAM;QACN8C,MAAM;QACNC,eAAe;QACf,4EAA4E;QAC5E,yCAAyC;QACzCxC,SAASyC;QACT,0EAA0E;QAC1E,0EAA0E;QAC1E,mBAAmB;QACnBnC,oBAAoB;QACpB,0DAA0D;QAC1DO,cAAc;QAEd,qBAAqB;QACrBhB,SAAS;QACT6C,MAAM;QACNC,MAAM;QACNC,MAAM;IACR;IACA,MAAM/C,UACJO,IAAIZ,OAAO,KAAK,OAAO;QAACY,IAAIR,IAAI;KAAC,GAAG;QAACQ,IAAIR,IAAI;QAAEQ,IAAIZ,OAAO;KAAC;IAC7DX,cAAcgE,GAAG,CAAChD,SAASkC;IAC3B,0EAA0E;IAC1E,mCAAmC;IACnCA,aAAalC,OAAO,GAAGA;IACvBd,cAAckB,GAAG,CAAC8B;IAClB,OAAOA;AACT;AAEA;;;CAGC,GACD,OAAO,SAASe,8BACdnD,GAAW,EACXa,IAAkB,EAClBC,KAA+B,EAC/BC,IAAY;IAEZ,MAAMb,UAAUU,yBAAyBC,MAAMC,OAAOC;IACtD,MAAMZ,gBAAgBoB,2BAA2BvB,KAAKE;IACtD,IAAIC,kBAAkB,MAAM;QAC1B,OAAOA;IACT;IACA,kDAAkD;IAClD,MAAMiC,eAAegB,gCAAgCtC,MAAMT,OAAO;IAClEf,gBAAgB4D,GAAG,CAAChD,SAASkC;IAC7B,0EAA0E;IAC1E,mCAAmC;IACnCA,aAAalC,OAAO,GAAGA;IACvBV,gBAAgBc,GAAG,CAAC8B;IACpB,OAAOA;AACT;AAEA,OAAO,SAASiB,qCACdrD,GAAW,EACXsD,SAA4B;IAE5B,MAAMrB,uBAAuBF,kCAAkC/B,KAAKsD;IACpE,IAAIrB,yBAAyB,MAAM;QACjC,OAAOA;IACT;IACA,MAAMG,eAAegB,gCAAgCE,UAAUjD,OAAO;IAEtE,4EAA4E;IAC5E,qEAAqE;IACrE,EAAE;IACF,2EAA2E;IAC3E,0EAA0E;IAC1E,EAAE;IACF,4EAA4E;IAC5E,6EAA6E;IAC7E,wEAAwE;IACxE,kEAAkE;IAClEiD,UAAU3B,YAAY,GAAGS;IAEzB,OAAOA;AACT;AAEA,OAAO,SAASP,mBACd7B,GAAW,EACXE,OAAoC,EACpCqD,cAAiC;IAEjC,4EAA4E;IAC5E,6EAA6E;IAC7E,yBAAyB;IACzB,6EAA6E;IAC7E,6EAA6E;IAC7E,iEAAiE;IACjE,MAAMpD,gBAAgBoB,2BAA2BvB,KAAKE;IACtD,IAAIC,kBAAkB,MAAM;QAC1B,IAAIoD,eAAeC,SAAS,IAAI,CAACrD,cAAcqD,SAAS,EAAE;YACxD,qEAAqE;YACrE,0DAA0D;YAC1D,0BAA0B;YAE1B,qEAAqE;YACrE,wEAAwE;YACxE,yEAAyE;YACzE,0EAA0E;YAC1E,yCAAyC;YACzC,MAAMC,gBAA2CF;YACjDE,cAAcf,MAAM;YACpBe,cAAcC,OAAO,GAAG;YACxBD,cAAcE,GAAG,GAAG;YACpB,OAAO;QACT;QACA,2CAA2C;QAC3C7B,uBAAuB3B,eAAeD;IACxC;IACAZ,gBAAgB4D,GAAG,CAAChD,SAASqD;IAC7B,0EAA0E;IAC1E,mCAAmC;IACnCA,eAAerD,OAAO,GAAGA;IACzBV,gBAAgBc,GAAG,CAACiD;IACpB,OAAOA;AACT;AAEA,OAAO,SAASH,gCACd/C,OAAe;IAEf,MAAMuD,aAAqC;QACzClB,MAAM;QACN,2EAA2E;QAC3E,sCAAsC;QACtCmB,aAAa;QACblC,cAAc;QACdgC,KAAK;QACLD,SAAS;QACTrD;QACAmD,WAAW;QACXlB,SAAS;QAET,qBAAqB;QACrBpC,SAAS;QACT6C,MAAM;QACNC,MAAM;QACNC,MAAM;IACR;IACA,OAAOW;AACT;AAEA,OAAO,SAASE,wBACdF,UAAkC,EAClCC,aAA4B;IAE5B,MAAMzB,eAAyCwB;IAC/CxB,aAAaM,MAAM;IACnBN,aAAayB,aAAa,GAAGA;IAC7B,OAAOzB;AACT;AAEA,SAAS7B,qBACPwD,KAAsB,EACtB7D,OAAkC;IAElC8D,iBAAiBD;IACjB7E,cAAc+E,MAAM,CAAC/D;IACrBd,cAAc6E,MAAM,CAACF;AACvB;AAEA,SAASjC,uBACPiC,KAAwB,EACxB7D,OAAoC;IAEpCgE,qBAAqBH;IACrBzE,gBAAgB2E,MAAM,CAAC/D;IACvBV,gBAAgByE,MAAM,CAACF;IACvB7B,kCAAkC6B;AACpC;AAEA,SAAS7B,kCAAkCF,KAAwB;IACjE,sEAAsE;IACtE,0EAA0E;IAC1E,6EAA6E;IAC7E,gBAAgB;IAChB,MAAMmC,sBAAsBnC,MAAML,YAAY;IAC9C,IAAIwC,wBAAwB,MAAM;QAChCD,qBAAqBC;QACrBnC,MAAML,YAAY,GAAG;IACvB;AACF;AAEA,OAAO,SAASyC,8BACdpC,KAAwB;IAExBE,kCAAkCF;IAClC,MAAM4B,aAAaR,gCAAgCpB,MAAM3B,OAAO;IAChE2B,MAAML,YAAY,GAAGiC;IACrB,OAAOA;AACT;AAEA,SAASvE,mBAAmB0E,KAAsB;IAChD,sDAAsD;IACtD,MAAM7D,UAAU6D,MAAM7D,OAAO;IAC7B,IAAIA,YAAY,MAAM;QACpB6D,MAAM7D,OAAO,GAAG;QAChB8D,iBAAiBD;QACjB7E,cAAc+E,MAAM,CAAC/D;IACvB;AACF;AAEA,SAAST,qBAAqBsE,KAAwB;IACpD,sDAAsD;IACtD,MAAM7D,UAAU6D,MAAM7D,OAAO;IAC7B,IAAIA,YAAY,MAAM;QACpB6D,MAAM7D,OAAO,GAAG;QAChBgE,qBAAqBH;QACrBzE,gBAAgB2E,MAAM,CAAC/D;IACzB;AACF;AAEA,SAASgE,qBAAqBH,KAAwB;IACpD,IAAIA,MAAMrB,MAAM,UAA4BqB,MAAMzB,OAAO,KAAK,MAAM;QAClE,4EAA4E;QAC5E,4EAA4E;QAC5E,aAAa;QACb,0EAA0E;QAC1E,iDAAiD;QACjDyB,MAAMzB,OAAO,CAAC+B,OAAO,CAAC;QACtBN,MAAMzB,OAAO,GAAG;IAClB;AACF;AAEA,SAAS0B,iBAAiBD,KAEzB;IACC,MAAMpB,eAAeoB,MAAMpB,YAAY;IACvC,IAAIA,iBAAiB,MAAM;QACzB,KAAK,MAAM9B,QAAQ8B,aAAc;YAC/B7E,iBAAiB+C;QACnB;QACAkD,MAAMpB,YAAY,GAAG;IACvB;AACF;AAEA,SAAS2B,uBACPP,KAAsB,EACtBjE,IAAe,EACf8C,IAAc,EACdC,aAAsB,EACtBxC,OAAe,EACfM,kBAA2B,EAC3B8B,YAAoB,EACpBvB,YAAqB;IAErB,MAAMqD,iBAA2CR;IACjDQ,eAAe7B,MAAM;IACrB6B,eAAezE,IAAI,GAAGA;IACtByE,eAAe3B,IAAI,GAAGA;IACtB2B,eAAe1B,aAAa,GAAGA;IAC/B0B,eAAelE,OAAO,GAAGA;IACzBkE,eAAe5D,kBAAkB,GAAGA;IACpC4D,eAAe9B,YAAY,GAAGA;IAC9B8B,eAAerD,YAAY,GAAGA;IAC9B8C,iBAAiBD;IACjB,OAAOQ;AACT;AAEA,SAASC,yBACPC,iBAAoE,EACpEd,GAAoB,EACpBD,OAAuD,EACvDrD,OAAe,EACfmD,SAAkB;IAElB,MAAMe,iBAA6CE;IACnDF,eAAe7B,MAAM;IACrB6B,eAAeZ,GAAG,GAAGA;IACrBY,eAAeb,OAAO,GAAGA;IACzBa,eAAelE,OAAO,GAAGA;IACzBkE,eAAef,SAAS,GAAGA;IAC3B,yDAAyD;IACzD,IAAIiB,kBAAkBnC,OAAO,KAAK,MAAM;QACtCmC,kBAAkBnC,OAAO,CAAC+B,OAAO,CAACE;QAClC,2CAA2C;QAC3CA,eAAejC,OAAO,GAAG;IAC3B;IACA,OAAOiC;AACT;AAEA,SAASG,sBACPX,KAA6B,EAC7B1D,OAAe;IAEf,MAAMoD,gBAAyCM;IAC/CN,cAAcf,MAAM;IACpBe,cAAcpD,OAAO,GAAGA;IACxB2D,iBAAiBD;AACnB;AAEA,SAASY,wBACPZ,KAA+B,EAC/B1D,OAAe;IAEf,MAAMoD,gBAA2CM;IACjDN,cAAcf,MAAM;IACpBe,cAAcpD,OAAO,GAAGA;IACxB,IAAI0D,MAAMzB,OAAO,KAAK,MAAM;QAC1B,0EAA0E;QAC1E,iDAAiD;QACjDyB,MAAMzB,OAAO,CAAC+B,OAAO,CAAC;QACtBN,MAAMzB,OAAO,GAAG;IAClB;AACF;AAEA,SAASsC,mCAAmCC,QAA0B;IACpE,OAAOC,+BAA+BD,SAAS/E,IAAI,EAAExB;AACvD;AAEA,SAASwG,+BACPC,QAAsB,EACtBtE,GAAW;IAEX,yEAAyE;IACzE,8EAA8E;IAC9E,4EAA4E;IAC5E,0EAA0E;IAC1E,uCAAuC;IACvC,IAAIuE,QAA0D;IAC9D,MAAMC,gBAAgBF,SAASC,KAAK;IACpC,IAAIC,kBAAkB,MAAM;QAC1BD,QAAQ,CAAC;QACT,IAAK,IAAIE,oBAAoBD,cAAe;YAC1C,MAAME,gBAAgBF,aAAa,CAACC,iBAAiB;YACrD,MAAME,eAAeD,cAAcE,OAAO;YAC1C,0EAA0E;YAC1E,uEAAuE;YACvE,wCAAwC;YACxC,MAAMC,WAAWlH,sBACfqC,KACAyE,kBACA7G,cAAc+G;YAEhBJ,KAAK,CAACE,iBAAiB,GAAGJ,+BACxBK,eACAG;QAEJ;IACF;IACA,OAAO;QACL7E;QACA4E,SAASN,SAASM,OAAO;QACzBL;QACAO,cAAcR,SAASQ,YAAY;IACrC;AACF;AAEA,SAASC,wCACPC,iBAAoC;IAEpC,OAAOC,oCACLD,mBACAnH;AAEJ;AAEA,SAASoH,oCACPD,iBAAoC,EACpChF,GAAW;IAEX,IAAIuE,QAA0D;IAE9D,MAAMW,iBAAiBF,iBAAiB,CAAC,EAAE;IAC3C,IAAK,IAAIP,oBAAoBS,eAAgB;QAC3C,MAAMC,mBAAmBD,cAAc,CAACT,iBAAiB;QACzD,MAAME,eAAeQ,gBAAgB,CAAC,EAAE;QACxC,0EAA0E;QAC1E,uEAAuE;QACvE,wCAAwC;QACxC,MAAMN,WAAWlH,sBACfqC,KACAyE,kBACA7G,cAAc+G;QAEhB,MAAMS,YAAYH,oCAChBE,kBACAN;QAEF,IAAIN,UAAU,MAAM;YAClBA,QAAQ;gBACN,CAACE,iBAAiB,EAAEW;YACtB;QACF,OAAO;YACLb,KAAK,CAACE,iBAAiB,GAAGW;QAC5B;IACF;IAEA,yEAAyE;IACzE,wEAAwE;IACxE,2EAA2E;IAC3E,0BAA0B;IAC1B,EAAE;IACF,6DAA6D;IAC7D,EAAE;IACF,8EAA8E;IAC9E,kEAAkE;IAClE,MAAMC,kBAAkBL,iBAAiB,CAAC,EAAE;IAC5C,MAAMM,6BACJ,OAAOD,oBAAoB,YAC3BA,gBAAgBE,UAAU,CAACtH,oBACvBA,mBACAoH;IAEN,OAAO;QACLrF;QACA4E,SAASU;QACTf;QACAO,cAAcE,iBAAiB,CAAC,EAAE,KAAK;IACzC;AACF;AAEA,OAAO,SAASQ,oCACdC,SAAoB;IAEpB,MAAMP,iBAAoD,CAAC;IAC3D,IAAIO,UAAUlB,KAAK,KAAK,MAAM;QAC5B,IAAK,MAAME,oBAAoBgB,UAAUlB,KAAK,CAAE;YAC9CW,cAAc,CAACT,iBAAiB,GAAGe,oCACjCC,UAAUlB,KAAK,CAACE,iBAAiB;QAErC;IACF;IACA,MAAMO,oBAAuC;QAC3CS,UAAUb,OAAO;QACjBM;QACA;QACA;QACAO,UAAUX,YAAY;KACvB;IACD,OAAOE;AACT;AAEA,OAAO,eAAeU,sBACpBpC,KAA6B,EAC7BlD,IAAkB;IAElB,6EAA6E;IAC7E,6EAA6E;IAC7E,wEAAwE;IACxE,cAAc;IACd,MAAMJ,MAAMI,KAAKJ,GAAG;IACpB,MAAMR,OAAOQ,IAAIR,IAAI;IACrB,MAAMJ,UAAUY,IAAIZ,OAAO;IAC3B,MAAMuG,cAAc;IAEpB,MAAMC,UAA0B;QAC9B,CAAC1I,WAAW,EAAE;QACd,CAACN,4BAA4B,EAAE;QAC/B,CAACC,oCAAoC,EAAE8I;IACzC;IACA,IAAIvG,YAAY,MAAM;QACpBwG,OAAO,CAAC5I,SAAS,GAAGoC;IACtB;IAEA,wEAAwE;IACxE,MAAMyG,MAAM,IAAIC,IAAItG;IACpB,MAAMuG,aAAa3H,qBACf4H,sCAAsCH,KAAKF,eAC3CE;IAEJ,IAAI;QACF,MAAMI,WAAW,MAAMC,sBAAsBH,YAAYH;QACzD,IACE,CAACK,YACD,CAACA,SAASE,EAAE,IACZ,uEAAuE;QACvE,yEAAyE;QACzE,oDAAoD;QACpDF,SAAShE,MAAM,KAAK,OACpB,CAACgE,SAASG,IAAI,EACd;YACA,wEAAwE;YACxE,uDAAuD;YACvDnC,sBAAsBX,OAAO+C,KAAK9G,GAAG,KAAK,KAAK;YAC/C,OAAO;QACT;QAEA,kEAAkE;QAClE,wEAAwE;QACxE,yEAAyE;QACzE,wEAAwE;QACxE,4EAA4E;QAC5E,yEAAyE;QACzE,EAAE;QACF,2EAA2E;QAC3E,2EAA2E;QAC3E,4EAA4E;QAC5E,0EAA0E;QAC1E,2EAA2E;QAC3E,4BAA4B;QAC5B,MAAMyC,eAAezE,kBACnB,IAAIuI,IACFG,SAASK,UAAU,GACfC,2CACE/G,MACAuG,WAAWvG,IAAI,EACfyG,SAASJ,GAAG,IAEdrG;QAIR,kEAAkE;QAClE,MAAMgH,aAAaP,SAASL,OAAO,CAACjG,GAAG,CAAC;QACxC,MAAMO,qBACJsG,eAAe,QAAQA,WAAWC,QAAQ,CAACzJ;QAE7C,4CAA4C;QAC5C,MAAM0J,SAAS5E;QAEf,0EAA0E;QAC1E,yEAAyE;QACzE,6BAA6B;QAC7B,MAAM6E,oBACJV,SAASL,OAAO,CAACjG,GAAG,CAAChD,8BAA8B,OACnD,yEAAyE;QACzE,wEAAwE;QACxE,2CAA2C;QAC3CyB;QAEF,IAAIuI,mBAAmB;YACrB,MAAMC,iBAAiBC,6BACrBZ,SAASG,IAAI,EACbM,OAAO9C,OAAO,EACd,SAASkD,qBAAqBtE,IAAI;gBAChC7D,cAAcoI,UAAU,CAACzD,OAAOd;YAClC;YAEF,MAAMwE,aAAa,MAAO5J,6BACxBwJ;YAEF,IAAII,WAAWC,OAAO,KAAK3J,iBAAiB;gBAC1C,qEAAqE;gBACrE,mEAAmE;gBACnE,0EAA0E;gBAC1E,sEAAsE;gBACtE,6BAA6B;gBAC7B2G,sBAAsBX,OAAO+C,KAAK9G,GAAG,KAAK,KAAK;gBAC/C,OAAO;YACT;YAEA,MAAM2H,cAAcF,WAAWG,SAAS,GAAG;YAC3CtD,uBACEP,OACAa,mCAAmC6C,aACnCA,WAAW7E,IAAI,EACf6E,WAAW5E,aAAa,EACxBiE,KAAK9G,GAAG,KAAK2H,aACbhH,oBACA8B,cACA2E;QAEJ,OAAO;YACL,gEAAgE;YAChE,gEAAgE;YAChE,sEAAsE;YACtE,yDAAyD;YACzD,uBAAuB;YACvB,MAAMC,iBAAiBC,6BACrBZ,SAASG,IAAI,EACbM,OAAO9C,OAAO,EACd,SAASkD,qBAAqBtE,IAAI;gBAChC7D,cAAcoI,UAAU,CAACzD,OAAOd;YAClC;YAEF,MAAMwE,aAAa,MAAO5J,6BACxBwJ;YAGFQ,kCACEf,KAAK9G,GAAG,IACRa,MACA6F,UACAe,YACA1D,OACApD,oBACA8B,cACA2E;QAEJ;QAEA,IAAI,CAACzG,sBAAsBd,YAAY,MAAM;YAC3C,yEAAyE;YACzE,wEAAwE;YACxE,6DAA6D;YAC7D,+BAA+B;YAC/B,EAAE;YACF,wEAAwE;YACxE,wEAAwE;YACxE,MAAMiI,iBAA4C;gBAAC7H;gBAAMJ;aAAQ;YACjE,MAAMkI,gBAAgB7I,cAAckB,GAAG,CAAC0H;YACxC,IAAIC,kBAAkBhE,OAAO;gBAC3B7E,cAAc+E,MAAM,CAAC6D;gBACrB,MAAME,aAAwC;oBAAC/H;iBAAK;gBACpDf,cAAcgE,GAAG,CAAC8E,YAAYjE;gBAC9B,sEAAsE;gBACtE,qEAAqE;gBACrE,sEAAsE;gBACtEA,MAAM7D,OAAO,GAAG8H;YAClB,OAAO;YACL,qEAAqE;YACrE,0DAA0D;YAC5D;QACF;QACA,wEAAwE;QACxE,wEAAwE;QACxE,OAAO;YAAEC,OAAO;YAAMd,QAAQA,OAAO7E,OAAO;QAAC;IAC/C,EAAE,OAAO4F,OAAO;QACd,uEAAuE;QACvE,yBAAyB;QACzBxD,sBAAsBX,OAAO+C,KAAK9G,GAAG,KAAK,KAAK;QAC/C,OAAO;IACT;AACF;AAEA,OAAO,eAAemI,wBACpBrH,KAA+B,EAC/B2D,iBAA2C,EAC3C2D,QAAuB,EACvBhC,WAAmB;IAEnB,6EAA6E;IAC7E,6EAA6E;IAC7E,wEAAwE;IACxE,cAAc;IACd,EAAE;IACF,0EAA0E;IAC1E,iBAAiB;IAEjB,4EAA4E;IAC5E,6EAA6E;IAC7E,6EAA6E;IAC7E,mEAAmE;IACnE,MAAME,MAAM,IAAIC,IAAIzF,MAAM2B,YAAY,EAAE2F,SAASnI,IAAI;IACrD,MAAMJ,UAAUuI,SAASvI,OAAO;IAEhC,MAAMwI,wBACJjC,gBAAgB9H,mBAEZ,iEAAiE;IACjE,oEAAoE;IACpE,qEAAqE;IACrE,gEAAgE;IAChE,qEAAqE;IACrE,YACA8H;IAEN,MAAMC,UAA0B;QAC9B,CAAC1I,WAAW,EAAE;QACd,CAACN,4BAA4B,EAAE;QAC/B,CAACC,oCAAoC,EAAE+K;IACzC;IACA,IAAIxI,YAAY,MAAM;QACpBwG,OAAO,CAAC5I,SAAS,GAAGoC;IACtB;IAEA,MAAM2G,aAAa3H,qBAEf4H,sCAAsCH,KAAK+B,yBAC3C/B;IACJ,IAAI;QACF,MAAMI,WAAW,MAAMC,sBAAsBH,YAAYH;QACzD,IACE,CAACK,YACD,CAACA,SAASE,EAAE,IACZF,SAAShE,MAAM,KAAK,OAAO,aAAa;QACxC,0EAA0E;QAC1E,yEAAyE;QACzE,oEAAoE;QACpE,uEAAuE;QACvE,0BAA0B;QACzBgE,SAASL,OAAO,CAACjG,GAAG,CAAChD,8BAA8B,OAClD,sEAAsE;QACtE,iEAAiE;QACjE,qDAAqD;QACrD,CAACyB,sBACH,CAAC6H,SAASG,IAAI,EACd;YACA,wEAAwE;YACxE,uDAAuD;YACvDlC,wBAAwBF,mBAAmBqC,KAAK9G,GAAG,KAAK,KAAK;YAC7D,OAAO;QACT;QAEA,4CAA4C;QAC5C,MAAMmH,SAAS5E;QAEf,2EAA2E;QAC3E,4DAA4D;QAC5D,MAAM8E,iBAAiBC,6BACrBZ,SAASG,IAAI,EACbM,OAAO9C,OAAO,EACd,SAASkD,qBAAqBtE,IAAI;YAChCzD,gBAAgBgI,UAAU,CAAC/C,mBAAmBxB;QAChD;QAEF,MAAMwE,aAAa,MAAO5J,6BACxBwJ;QAEF,IAAII,WAAWC,OAAO,KAAK3J,iBAAiB;YAC1C,qEAAqE;YACrE,mEAAmE;YACnE,0EAA0E;YAC1E,sEAAsE;YACtE,6BAA6B;YAC7B4G,wBAAwBF,mBAAmBqC,KAAK9G,GAAG,KAAK,KAAK;YAC7D,OAAO;QACT;QACA,OAAO;YACLiI,OAAOzD,yBACLC,mBACAgD,WAAW9D,GAAG,EACd8D,WAAW/D,OAAO,EAClB,sEAAsE;YACtE,yCAAyC;YACzC5C,MAAMT,OAAO,EACboH,WAAWjE,SAAS;YAEtB,wEAAwE;YACxE,wEAAwE;YACxE2D,QAAQA,OAAO7E,OAAO;QACxB;IACF,EAAE,OAAO4F,OAAO;QACd,uEAAuE;QACvE,yBAAyB;QACzBvD,wBAAwBF,mBAAmBqC,KAAK9G,GAAG,KAAK,KAAK;QAC7D,OAAO;IACT;AACF;AAEA,OAAO,eAAesI,0CACpBzH,IAAkB,EAClBC,KAA+B,EAC/B+C,aAA4B,EAC5B0E,kBAAqC,EACrCC,cAAqD;IAErD,MAAMlC,MAAM,IAAIC,IAAIzF,MAAM2B,YAAY,EAAE5B,KAAKJ,GAAG,CAACR,IAAI;IACrD,MAAMJ,UAAUgB,KAAKJ,GAAG,CAACZ,OAAO;IAChC,MAAMwG,UAA0B;QAC9B,CAAC1I,WAAW,EAAE;QACd,CAACH,8BAA8B,EAAEiL,mBAC/BC,KAAKC,SAAS,CAACJ;IAEnB;IACA,IAAI1I,YAAY,MAAM;QACpBwG,OAAO,CAAC5I,SAAS,GAAGoC;IACtB;IACA,wEAAwE;IACxE,yEAAyE;IACzE,2EAA2E;IAC3E,qCAAqC;IACrC,IAAIgE,qBAAsC;QACxCwC,OAAO,CAAChJ,4BAA4B,GAAG;IACzC;IACA,IAAI;QACF,MAAMqJ,WAAW,MAAMC,sBAAsBL,KAAKD;QAClD,IAAI,CAACK,YAAY,CAACA,SAASE,EAAE,IAAI,CAACF,SAASG,IAAI,EAAE;YAC/C,wEAAwE;YACxE,uDAAuD;YACvD+B,mCAAmCJ,gBAAgB1B,KAAK9G,GAAG,KAAK,KAAK;YACrE,OAAO;QACT;QAEA,4CAA4C;QAC5C,MAAMmH,SAAS5E;QAEf,IAAIsG,mBAA6D;QACjE,MAAMxB,iBAAiBC,6BACrBZ,SAASG,IAAI,EACbM,OAAO9C,OAAO,EACd,SAASkD,qBAAqBuB,uBAAuB;YACnD,mEAAmE;YACnE,iEAAiE;YACjE,0CAA0C;YAC1C,IAAID,qBAAqB,MAAM;gBAC7B,0DAA0D;gBAC1D,iBAAiB;gBACjB;YACF;YACA,MAAME,cAAcD,0BAA0BD,iBAAiBG,MAAM;YACrE,KAAK,MAAMjF,SAAS8E,iBAAkB;gBACpCrJ,gBAAgBgI,UAAU,CAACzD,OAAOgF;YACpC;QACF;QAEF,MAAMtB,aAAa,MAAO5J,6BACxBwJ;QAGF,yEAAyE;QACzE,oCAAoC;QACpC,MAAM4B,oBAAoB;QAE1B,yEAAyE;QACzE,4EAA4E;QAC5E,oCAAoC;QACpCJ,mBAAmBK,oCACjBpC,KAAK9G,GAAG,IACRa,MACA6F,UACAe,YACAwB,mBACAnI,OACA0H;QAGF,wEAAwE;QACxE,wEAAwE;QACxE,OAAO;YAAEP,OAAO;YAAMd,QAAQA,OAAO7E,OAAO;QAAC;IAC/C,EAAE,OAAO4F,OAAO;QACdU,mCAAmCJ,gBAAgB1B,KAAK9G,GAAG,KAAK,KAAK;QACrE,OAAO;IACT;AACF;AAEA,SAAS6H,kCACP7H,GAAW,EACXa,IAAkB,EAClB6F,QAAkB,EAClBe,UAAoC,EACpC1D,KAA6B,EAC7BpD,kBAA2B,EAC3B8B,YAAoB,EACpB2E,iBAA0B;IAE1B,IAAIK,WAAW0B,CAAC,KAAKpL,iBAAiB;QACpC,qEAAqE;QACrE,mEAAmE;QACnE,0EAA0E;QAC1E,sEAAsE;QACtE,6BAA6B;QAC7B2G,sBAAsBX,OAAO/D,MAAM,KAAK;QACxC;IACF;IACA,MAAMoJ,6BAA6B7K,oBAAoBkJ,WAAW4B,CAAC;IACnE,IACE,mEAAmE;IACnE,kBAAkB;IAClB,OAAOD,+BAA+B,YACtCA,2BAA2BJ,MAAM,KAAK,GACtC;QACAtE,sBAAsBX,OAAO/D,MAAM,KAAK;QACxC;IACF;IACA,MAAMsJ,aAAaF,0BAA0B,CAAC,EAAE;IAChD,IAAI,CAACE,WAAWC,YAAY,EAAE;QAC5B,8BAA8B;QAC9B7E,sBAAsBX,OAAO/D,MAAM,KAAK;QACxC;IACF;IAEA,MAAMyF,oBAAoB6D,WAAWxJ,IAAI;IACzC,4BAA4B;IAC5B,MAAM0J,yBAAyB9C,SAASL,OAAO,CAACjG,GAAG,CACjD7C;IAEF,MAAMoK,cACJ6B,2BAA2B,OACvBC,SAASD,wBAAwB,MAAM,OACvChL;IAEN,6EAA6E;IAC7E,wEAAwE;IACxE,8EAA8E;IAC9E,qCAAqC;IACrC,MAAMyK,oBACJvC,SAASL,OAAO,CAACjG,GAAG,CAAChD,8BAA8B;IAErD,MAAMmH,iBAAiBD,uBACrBP,OACAyB,wCAAwCC,oBACxC6D,WAAW1G,IAAI,EACfqG,mBACAjJ,MAAM2H,aACNhH,oBACA8B,cACA2E;IAGF,2EAA2E;IAC3E,qEAAqE;IACrE,EAAE;IACF,0EAA0E;IAC1E,0EAA0E;IAC1E,4EAA4E;IAC5E,yEAAyE;IACzE,0EAA0E;IAC1E,2EAA2E;IAC3E8B,oCACElJ,KACAa,MACA6F,UACAe,YACAwB,mBACA1E,gBACA;AAEJ;AAEA,SAASqE,mCACPc,OAAuC,EACvCrJ,OAAe;IAEf,MAAMwI,mBAAmB,EAAE;IAC3B,KAAK,MAAM9E,SAAS2F,QAAQC,MAAM,GAAI;QACpC,IAAI5F,MAAMrB,MAAM,QAA0B;YACxCiC,wBAAwBZ,OAAO1D;QACjC,OAAO,IAAI0D,MAAMrB,MAAM,QAA4B;YACjDmG,iBAAiBe,IAAI,CAAC7F;QACxB;IACF;IACA,OAAO8E;AACT;AAEA,SAASK,oCACPlJ,GAAW,EACXa,IAAkB,EAClB6F,QAAkB,EAClBe,UAAoC,EACpCwB,iBAA0B,EAC1BnI,KAA+B,EAC/B0H,cAA4D;IAE5D,IAAIf,WAAW0B,CAAC,KAAKpL,iBAAiB;QACpC,qEAAqE;QACrE,mEAAmE;QACnE,0EAA0E;QAC1E,sEAAsE;QACtE,6BAA6B;QAC7B,IAAIyK,mBAAmB,MAAM;YAC3BI,mCAAmCJ,gBAAgBxI,MAAM,KAAK;QAChE;QACA,OAAO;IACT;IACA,MAAM6J,cAActL,oBAAoBkJ,WAAW4B,CAAC;IACpD,IAAI,OAAOQ,gBAAgB,UAAU;QACnC,wEAAwE;QACxE,4EAA4E;QAC5E,OAAO;IACT;IACA,KAAK,MAAMP,cAAcO,YAAa;QACpC,MAAMC,WAAWR,WAAWQ,QAAQ;QACpC,IAAIA,aAAa,MAAM;YACrB,uEAAuE;YACvE,oEAAoE;YACpE,EAAE;YACF,sEAAsE;YACtE,6CAA6C;YAC7C,EAAE;YACF,6DAA6D;YAC7D,MAAM1D,cAAckD,WAAWlD,WAAW;YAC1C,IAAI2D,aAAazL;YACjB,IAAK,IAAI0L,IAAI,GAAGA,IAAI5D,YAAY4C,MAAM,EAAEgB,KAAK,EAAG;gBAC9C,MAAM9E,mBAA2BkB,WAAW,CAAC4D,EAAE;gBAC/C,MAAM3E,UAAoCe,WAAW,CAAC4D,IAAI,EAAE;gBAC5DD,aAAa3L,sBACX2L,YACA7E,kBACA7G,cAAcgH;YAElB;YACA,MAAMmE,yBAAyB9C,SAASL,OAAO,CAACjG,GAAG,CACjD7C;YAEF,MAAMoK,cACJ6B,2BAA2B,OACvBC,SAASD,wBAAwB,MAAM,OACvChL;YACNyL,uBACEjK,KACAa,MACAC,OACAd,MAAM2H,aACNmC,UACAb,mBACAc,YACAvB;QAEJ;IACF;IACA,uEAAuE;IACvE,4EAA4E;IAC5E,sCAAsC;IACtC,4EAA4E;IAC5E,2EAA2E;IAC3E,yEAAyE;IACzE,8EAA8E;IAC9E,oEAAoE;IACpE,IAAIA,mBAAmB,MAAM;QAC3B,MAAMK,mBAAmBD,mCACvBJ,gBACAxI,MAAM,KAAK;QAEb,OAAO6I;IACT;IACA,OAAO;AACT;AAEA,SAASoB,uBACPjK,GAAW,EACXa,IAAkB,EAClBC,KAA+B,EAC/BT,OAAe,EACfyJ,QAA2B,EAC3Bb,iBAA0B,EAC1BxI,GAAW,EACXyJ,yBAAuE;IAEvE,wEAAwE;IACxE,2EAA2E;IAC3E,0EAA0E;IAC1E,uEAAuE;IACvE,4EAA4E;IAC5E,MAAMvG,MAAMmG,QAAQ,CAAC,EAAE;IACvB,MAAMpG,UAAUoG,QAAQ,CAAC,EAAE;IAC3B,MAAMtG,YAAYG,QAAQ,QAAQsF;IAElC,0EAA0E;IAC1E,4EAA4E;IAC5E,+DAA+D;IAC/D,MAAMkB,aACJD,8BAA8B,OAC1BA,0BAA0B9J,GAAG,CAACK,OAC9B2J;IACN,IAAID,eAAeC,WAAW;QAC5B5F,yBAAyB2F,YAAYxG,KAAKD,SAASrD,SAASmD;IAC9D,OAAO;QACL,0DAA0D;QAC1D,MAAM6G,mBAAmBlH,8BACvBnD,KACAa,MACAC,OACAL;QAEF,IAAI4J,iBAAiB3H,MAAM,QAAwB;YACjD,oDAAoD;YACpD,MAAM4H,WAAWD;YACjB7F,yBAAyB8F,UAAU3G,KAAKD,SAASrD,SAASmD;QAC5D,OAAO;YACL,iEAAiE;YACjE,+CAA+C;YAC/C,MAAM8G,WAAW9F,yBACfpB,gCAAgC/C,UAChCsD,KACAD,SACArD,SACAmD;YAEF3B,mBACE7B,KACAY,yBAAyBC,MAAMC,OAAOL,MACtC6J;QAEJ;IACF;IACA,mDAAmD;IACnD,MAAMC,mBAAmBT,QAAQ,CAAC,EAAE;IACpC,IAAIS,qBAAqB,MAAM;QAC7B,IAAK,MAAMrF,oBAAoBqF,iBAAkB;YAC/C,MAAMC,gBAAgBD,gBAAgB,CAACrF,iBAAiB;YACxD,IAAIsF,kBAAkB,MAAM;gBAC1B,MAAMpF,eAAeoF,aAAa,CAAC,EAAE;gBACrCP,uBACEjK,KACAa,MACAC,OACAT,SACAmK,eACAvB,mBACA7K,sBACEqC,KACAyE,kBACA7G,cAAc+G,gBAEhB8E;YAEJ;QACF;IACF;AACF;AAEA,eAAevD,sBACbL,GAAQ,EACRD,OAAuB;IAEvB,MAAMoE,gBAAgB;IACtB,MAAM/D,WAAW,MAAM9I,YAAY0I,KAAKD,SAASoE;IACjD,IAAI,CAAC/D,SAASE,EAAE,EAAE;QAChB,OAAO;IACT;IAEA,yBAAyB;IACzB,IAAI/H,oBAAoB;IACtB,0EAA0E;IAC1E,2EAA2E;IAC3E,2EAA2E;IAC3E,sDAAsD;IACxD,OAAO;QACL,MAAM6L,cAAchE,SAASL,OAAO,CAACjG,GAAG,CAAC;QACzC,MAAMuK,mBACJD,eAAeA,YAAY1E,UAAU,CAACtI;QACxC,IAAI,CAACiN,kBAAkB;YACrB,OAAO;QACT;IACF;IACA,OAAOjE;AACT;AAEA,SAASY,6BACPsD,oBAAgD,EAChDC,aAAyB,EACzBtD,oBAA4C;IAE5C,0EAA0E;IAC1E,4EAA4E;IAC5E,uEAAuE;IACvE,0EAA0E;IAC1E,8DAA8D;IAC9D,2CAA2C;IAC3C,EAAE;IACF,2EAA2E;IAC3E,0EAA0E;IAC1E,8EAA8E;IAC9E,+BAA+B;IAC/B,EAAE;IACF,8EAA8E;IAC9E,iCAAiC;IACjC,IAAIuD,kBAAkB;IACtB,MAAMC,SAASH,qBAAqBI,SAAS;IAC7C,OAAO,IAAIC,eAAe;QACxB,MAAMC,MAAKC,UAAU;YACnB,MAAO,KAAM;gBACX,MAAM,EAAEC,IAAI,EAAEnD,KAAK,EAAE,GAAG,MAAM8C,OAAOM,IAAI;gBACzC,IAAI,CAACD,MAAM;oBACT,mEAAmE;oBACnE,mBAAmB;oBACnBD,WAAWG,OAAO,CAACrD;oBAEnB,+DAA+D;oBAC/D,kEAAkE;oBAClE,qEAAqE;oBACrE,6CAA6C;oBAC7C6C,mBAAmB7C,MAAMsD,UAAU;oBACnChE,qBAAqBuD;oBACrB;gBACF;gBACA,qEAAqE;gBACrE,sDAAsD;gBACtDD;gBACA;YACF;QACF;IACF;AACF;AAEA,SAASpE,sCACPH,GAAQ,EACRF,WAAmB;IAEnB,IAAIvH,oBAAoB;QACtB,yEAAyE;QACzE,0DAA0D;QAC1D,MAAM2M,YAAY,IAAIjF,IAAID;QAC1B,MAAMmF,WAAWD,UAAUE,QAAQ,CAACvK,QAAQ,CAAC,OACzCqK,UAAUE,QAAQ,CAACC,SAAS,CAAC,GAAG,CAAC,KACjCH,UAAUE,QAAQ;QACtB,MAAME,uBACJzN,yCAAyCiI;QAC3CoF,UAAUE,QAAQ,GAAG,AAAGD,WAAS,MAAGG;QACpC,OAAOJ;IACT;IACA,OAAOlF;AACT;AAEA,SAASU,2CACP/G,IAAY,EACZuG,UAAkB,EAClBqF,WAAmB;IAEnB,IAAIhN,oBAAoB;QACtB,oDAAoD;QACpD,EAAE;QACF,sEAAsE;QACtE,0EAA0E;QAC1E,uCAAuC;QACvC,EAAE;QACF,sEAAsE;QACtE,mBAAmB;QACnB,MAAMuH,cAAcI,WAAWmF,SAAS,CAAC1L,KAAK+I,MAAM;QACpD,IAAI6C,YAAY1K,QAAQ,CAACiF,cAAc;YACrC,qEAAqE;YACrE,OAAOyF,YAAYF,SAAS,CAAC,GAAGE,YAAY7C,MAAM,GAAG5C,YAAY4C,MAAM;QACzE,OAAO;QACL,wEAAwE;QACxE,uEAAuE;QACvE,wEAAwE;QACxE,wEAAwE;QACxE,qDAAqD;QACvD;IACF;IACA,OAAO6C;AACT;AAEA,SAAStJ;IACP,iDAAiD;IACjD,IAAI8B;IACJ,IAAIyH;IACJ,MAAMxJ,UAAU,IAAIyJ,QAAW,CAACC,KAAKC;QACnC5H,UAAU2H;QACVF,SAASG;IACX;IACA,OAAO;QAAE5H,SAASA;QAAUyH,QAAQA;QAASxJ;IAAQ;AACvD"}