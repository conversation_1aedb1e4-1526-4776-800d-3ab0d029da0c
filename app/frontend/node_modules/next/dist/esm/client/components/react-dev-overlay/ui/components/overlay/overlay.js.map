{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/overlay/overlay.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { lock, unlock } from './body-locker'\n\nexport type OverlayProps = {\n  children?: React.ReactNode\n  className?: string\n  fixed?: boolean\n}\n\nconst Overlay: React.FC<OverlayProps> = function Overlay({\n  className,\n  children,\n  fixed,\n  ...props\n}) {\n  React.useEffect(() => {\n    lock()\n    return () => {\n      unlock()\n    }\n  }, [])\n\n  return (\n    <div data-nextjs-dialog-overlay className={className} {...props}>\n      <div\n        data-nextjs-dialog-backdrop\n        data-nextjs-dialog-backdrop-fixed={fixed ? true : undefined}\n      />\n      {children}\n    </div>\n  )\n}\n\nexport { Overlay }\n"], "names": ["React", "lock", "unlock", "Overlay", "className", "children", "fixed", "props", "useEffect", "div", "data-nextjs-dialog-overlay", "data-nextjs-dialog-backdrop", "data-nextjs-dialog-backdrop-fixed", "undefined"], "mappings": ";AAAA,YAAYA,WAAW,QAAO;AAC9B,SAASC,IAAI,EAAEC,MAAM,QAAQ,gBAAe;AAQ5C,MAAMC,UAAkC,SAASA,QAAQ,KAKxD;IALwD,IAAA,EACvDC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACL,GAAGC,OACJ,GALwD;IAMvDP,MAAMQ,SAAS,CAAC;QACdP;QACA,OAAO;YACLC;QACF;IACF,GAAG,EAAE;IAEL,qBACE,MAACO;QAAIC,4BAA0B;QAACN,WAAWA;QAAY,GAAGG,KAAK;;0BAC7D,KAACE;gBACCE,6BAA2B;gBAC3BC,qCAAmCN,QAAQ,OAAOO;;YAEnDR;;;AAGP;AAEA,SAASF,OAAO,GAAE"}