{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/utils/get-source-map-from-file.ts"], "sourcesContent": ["import fs from 'fs/promises'\nimport path from 'path'\nimport url from 'url'\nimport type { RawSourceMap } from 'next/dist/compiled/source-map08'\nimport dataUriToBuffer from 'next/dist/compiled/data-uri-to-buffer'\nimport { getSourceMapUrl } from './get-source-map-url'\n\nexport async function getSourceMapFromFile(\n  filename: string\n): Promise<RawSourceMap | undefined> {\n  filename = filename.startsWith('file://')\n    ? url.fileURLToPath(filename)\n    : filename\n\n  let fileContents: string\n\n  try {\n    fileContents = await fs.readFile(filename, 'utf-8')\n  } catch (error) {\n    throw new Error(`Failed to read file contents of ${filename}.`, {\n      cause: error,\n    })\n  }\n\n  const sourceUrl = getSourceMapUrl(fileContents)\n\n  if (!sourceUrl) {\n    return undefined\n  }\n\n  if (sourceUrl.startsWith('data:')) {\n    let buffer: dataUriToBuffer.MimeBuffer\n\n    try {\n      buffer = dataUriTo<PERSON>uffer(sourceUrl)\n    } catch (error) {\n      throw new Error(`Failed to parse source map URL for ${filename}.`, {\n        cause: error,\n      })\n    }\n\n    if (buffer.type !== 'application/json') {\n      throw new Error(\n        `Unknown source map type for ${filename}: ${buffer.typeFull}.`\n      )\n    }\n\n    try {\n      return JSON.parse(buffer.toString())\n    } catch (error) {\n      throw new Error(`Failed to parse source map for ${filename}.`, {\n        cause: error,\n      })\n    }\n  }\n\n  const sourceMapFilename = path.resolve(\n    path.dirname(filename),\n    decodeURIComponent(sourceUrl)\n  )\n\n  try {\n    const sourceMapContents = await fs.readFile(sourceMapFilename, 'utf-8')\n\n    return JSON.parse(sourceMapContents.toString())\n  } catch (error) {\n    throw new Error(`Failed to parse source map ${sourceMapFilename}.`, {\n      cause: error,\n    })\n  }\n}\n"], "names": ["fs", "path", "url", "dataUriToBuffer", "getSourceMapUrl", "getSourceMapFromFile", "filename", "startsWith", "fileURLToPath", "fileContents", "readFile", "error", "Error", "cause", "sourceUrl", "undefined", "buffer", "type", "typeFull", "JSON", "parse", "toString", "sourceMapFilename", "resolve", "dirname", "decodeURIComponent", "sourceMapContents"], "mappings": "AAAA,OAAOA,QAAQ,cAAa;AAC5B,OAAOC,UAAU,OAAM;AACvB,OAAOC,SAAS,MAAK;AAErB,OAAOC,qBAAqB,wCAAuC;AACnE,SAASC,eAAe,QAAQ,uBAAsB;AAEtD,OAAO,eAAeC,qBACpBC,QAAgB;IAEhBA,WAAWA,SAASC,UAAU,CAAC,aAC3BL,IAAIM,aAAa,CAACF,YAClBA;IAEJ,IAAIG;IAEJ,IAAI;QACFA,eAAe,MAAMT,GAAGU,QAAQ,CAACJ,UAAU;IAC7C,EAAE,OAAOK,OAAO;QACd,MAAM,qBAEJ,CAFI,IAAIC,MAAM,AAAC,qCAAkCN,WAAS,KAAI;YAC9DO,OAAOF;QACT,IAFM,qBAAA;mBAAA;wBAAA;0BAAA;QAEL;IACH;IAEA,MAAMG,YAAYV,gBAAgBK;IAElC,IAAI,CAACK,WAAW;QACd,OAAOC;IACT;IAEA,IAAID,UAAUP,UAAU,CAAC,UAAU;QACjC,IAAIS;QAEJ,IAAI;YACFA,SAASb,gBAAgBW;QAC3B,EAAE,OAAOH,OAAO;YACd,MAAM,qBAEJ,CAFI,IAAIC,MAAM,AAAC,wCAAqCN,WAAS,KAAI;gBACjEO,OAAOF;YACT,IAFM,qBAAA;uBAAA;4BAAA;8BAAA;YAEL;QACH;QAEA,IAAIK,OAAOC,IAAI,KAAK,oBAAoB;YACtC,MAAM,qBAEL,CAFK,IAAIL,MACR,AAAC,iCAA8BN,WAAS,OAAIU,OAAOE,QAAQ,GAAC,MADxD,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI;YACF,OAAOC,KAAKC,KAAK,CAACJ,OAAOK,QAAQ;QACnC,EAAE,OAAOV,OAAO;YACd,MAAM,qBAEJ,CAFI,IAAIC,MAAM,AAAC,oCAAiCN,WAAS,KAAI;gBAC7DO,OAAOF;YACT,IAFM,qBAAA;uBAAA;4BAAA;8BAAA;YAEL;QACH;IACF;IAEA,MAAMW,oBAAoBrB,KAAKsB,OAAO,CACpCtB,KAAKuB,OAAO,CAAClB,WACbmB,mBAAmBX;IAGrB,IAAI;QACF,MAAMY,oBAAoB,MAAM1B,GAAGU,QAAQ,CAACY,mBAAmB;QAE/D,OAAOH,KAAKC,KAAK,CAACM,kBAAkBL,QAAQ;IAC9C,EAAE,OAAOV,OAAO;QACd,MAAM,qBAEJ,CAFI,IAAIC,MAAM,AAAC,gCAA6BU,oBAAkB,KAAI;YAClET,OAAOF;QACT,IAFM,qBAAA;mBAAA;wBAAA;0BAAA;QAEL;IACH;AACF"}