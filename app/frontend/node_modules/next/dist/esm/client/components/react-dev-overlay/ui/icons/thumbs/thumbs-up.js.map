{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/icons/thumbs/thumbs-up.tsx"], "sourcesContent": ["import type { ComponentProps } from 'react'\n\nexport function ThumbsUp(props: ComponentProps<'svg'>) {\n  return (\n    <svg\n      width=\"16\"\n      height=\"16\"\n      viewBox=\"0 0 16 16\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className=\"thumbs-up-icon\"\n      {...props}\n    >\n      <g id=\"thumb-up-16\">\n        <path\n          id=\"Union\"\n          fillRule=\"evenodd\"\n          clipRule=\"evenodd\"\n          d=\"M6.89531 2.23959C6.72984 2.1214 6.5 2.23968 6.5 2.44303V5.24989C6.5 6.21639 5.7165 6.99989 4.75 6.99989H2.5V13.4999H12.1884C12.762 13.4999 13.262 13.1095 13.4011 12.5531L14.4011 8.55306C14.5984 7.76412 14.0017 6.99989 13.1884 6.99989H9.25H8.5V6.24989V3.51446C8.5 3.43372 8.46101 3.35795 8.39531 3.31102L6.89531 2.23959ZM5 2.44303C5 1.01963 6.6089 0.191656 7.76717 1.01899L9.26717 2.09042C9.72706 2.41892 10 2.94929 10 3.51446V5.49989H13.1884C14.9775 5.49989 16.2903 7.18121 15.8563 8.91686L14.8563 12.9169C14.5503 14.1411 13.4503 14.9999 12.1884 14.9999H1.75H1V14.2499V6.24989V5.49989H1.75H4.75C4.88807 5.49989 5 5.38796 5 5.24989V2.44303Z\"\n          fill=\"currentColor\"\n        />\n      </g>\n    </svg>\n  )\n}\n"], "names": ["ThumbsUp", "props", "svg", "width", "height", "viewBox", "fill", "xmlns", "className", "g", "id", "path", "fillRule", "clipRule", "d"], "mappings": ";AAEA,OAAO,SAASA,SAASC,KAA4B;IACnD,qBACE,KAACC;QACCC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACRC,MAAK;QACLC,OAAM;QACNC,WAAU;QACT,GAAGP,KAAK;kBAET,cAAA,KAACQ;YAAEC,IAAG;sBACJ,cAAA,KAACC;gBACCD,IAAG;gBACHE,UAAS;gBACTC,UAAS;gBACTC,GAAE;gBACFR,MAAK;;;;AAKf"}