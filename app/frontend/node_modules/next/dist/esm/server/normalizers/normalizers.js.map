{"version": 3, "sources": ["../../../src/server/normalizers/normalizers.ts"], "sourcesContent": ["import type { Normalizer } from './normalizer'\n\n/**\n * Normalizers combines many normalizers into a single normalizer interface that\n * will normalize the inputted pathname with each normalizer in order.\n */\nexport class Normalizers implements Normalizer {\n  constructor(private readonly normalizers: Array<Normalizer> = []) {}\n\n  public push(normalizer: Normalizer) {\n    this.normalizers.push(normalizer)\n  }\n\n  public normalize(pathname: string): string {\n    return this.normalizers.reduce<string>(\n      (normalized, normalizer) => normalizer.normalize(normalized),\n      pathname\n    )\n  }\n}\n"], "names": ["Normalizers", "constructor", "normalizers", "push", "normalizer", "normalize", "pathname", "reduce", "normalized"], "mappings": "AAEA;;;CAGC,GACD,OAAO,MAAMA;IACXC,YAAY,AAAiBC,cAAiC,EAAE,CAAE;aAArCA,cAAAA;IAAsC;IAE5DC,KAAKC,UAAsB,EAAE;QAClC,IAAI,CAACF,WAAW,CAACC,IAAI,CAACC;IACxB;IAEOC,UAAUC,QAAgB,EAAU;QACzC,OAAO,IAAI,CAACJ,WAAW,CAACK,MAAM,CAC5B,CAACC,YAAYJ,aAAeA,WAAWC,SAAS,CAACG,aACjDF;IAEJ;AACF"}