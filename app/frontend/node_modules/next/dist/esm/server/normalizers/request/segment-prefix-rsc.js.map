{"version": 3, "sources": ["../../../../src/server/normalizers/request/segment-prefix-rsc.ts"], "sourcesContent": ["import type { PathnameNormalizer } from './pathname-normalizer'\n\nimport {\n  RSC_SEGMENT_SUFFIX,\n  RSC_SEGMENTS_DIR_SUFFIX,\n} from '../../../lib/constants'\n\nconst PATTERN = new RegExp(\n  `^(/.*)${RSC_SEGMENTS_DIR_SUFFIX}(/.*)${RSC_SEGMENT_SUFFIX}$`\n)\n\nexport class SegmentPrefixRSCPathnameNormalizer implements PathnameNormalizer {\n  public match(pathname: string): boolean {\n    return PATTERN.test(pathname)\n  }\n\n  public extract(pathname: string) {\n    const match = pathname.match(PATTERN)\n    if (!match) return null\n\n    return { originalPathname: match[1], segmentPath: match[2] }\n  }\n\n  public normalize(pathname: string): string {\n    const match = this.extract(pathname)\n    if (!match) return pathname\n\n    return match.originalPathname\n  }\n}\n"], "names": ["RSC_SEGMENT_SUFFIX", "RSC_SEGMENTS_DIR_SUFFIX", "PATTERN", "RegExp", "SegmentPrefixRSCPathnameNormalizer", "match", "pathname", "test", "extract", "originalPathname", "segmentPath", "normalize"], "mappings": "AAEA,SACEA,kBAAkB,EAClBC,uBAAuB,QAClB,yBAAwB;AAE/B,MAAMC,UAAU,IAAIC,OAClB,CAAC,MAAM,EAAEF,wBAAwB,KAAK,EAAED,mBAAmB,CAAC,CAAC;AAG/D,OAAO,MAAMI;IACJC,MAAMC,QAAgB,EAAW;QACtC,OAAOJ,QAAQK,IAAI,CAACD;IACtB;IAEOE,QAAQF,QAAgB,EAAE;QAC/B,MAAMD,QAAQC,SAASD,KAAK,CAACH;QAC7B,IAAI,CAACG,OAAO,OAAO;QAEnB,OAAO;YAAEI,kBAAkBJ,KAAK,CAAC,EAAE;YAAEK,aAAaL,KAAK,CAAC,EAAE;QAAC;IAC7D;IAEOM,UAAUL,QAAgB,EAAU;QACzC,MAAMD,QAAQ,IAAI,CAACG,OAAO,CAACF;QAC3B,IAAI,CAACD,OAAO,OAAOC;QAEnB,OAAOD,MAAMI,gBAAgB;IAC/B;AACF"}