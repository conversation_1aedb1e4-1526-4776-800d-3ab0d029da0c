{"version": 3, "sources": ["../../../src/server/lib/app-dir-module.ts"], "sourcesContent": ["import type { AppDirModules } from '../../build/webpack/loaders/next-app-loader'\nimport { DEFAULT_SEGMENT_KEY } from '../../shared/lib/segment'\n\n/**\n * LoaderTree is generated in next-app-loader.\n */\nexport type LoaderTree = [\n  segment: string,\n  parallelRoutes: { [parallelRouterKey: string]: LoaderTree },\n  modules: AppDirModules,\n]\n\nexport async function getLayoutOrPageModule(loaderTree: LoaderTree) {\n  const { layout, page, defaultPage } = loaderTree[2]\n  const isLayout = typeof layout !== 'undefined'\n  const isPage = typeof page !== 'undefined'\n  const isDefaultPage =\n    typeof defaultPage !== 'undefined' && loaderTree[0] === DEFAULT_SEGMENT_KEY\n\n  let mod = undefined\n  let modType: 'layout' | 'page' | undefined = undefined\n  let filePath = undefined\n\n  if (isLayout) {\n    mod = await layout[0]()\n    modType = 'layout'\n    filePath = layout[1]\n  } else if (isPage) {\n    mod = await page[0]()\n    modType = 'page'\n    filePath = page[1]\n  } else if (isDefaultPage) {\n    mod = await defaultPage[0]()\n    modType = 'page'\n    filePath = defaultPage[1]\n  }\n\n  return { mod, modType, filePath }\n}\n\nexport async function getComponentTypeModule(\n  loaderTree: LoaderTree,\n  moduleType: 'layout' | 'not-found' | 'forbidden' | 'unauthorized'\n) {\n  const { [moduleType]: module } = loaderTree[2]\n  if (typeof module !== 'undefined') {\n    return await module[0]()\n  }\n  return undefined\n}\n"], "names": ["DEFAULT_SEGMENT_KEY", "getLayoutOrPageModule", "loaderTree", "layout", "page", "defaultPage", "isLayout", "isPage", "isDefaultPage", "mod", "undefined", "modType", "filePath", "getComponentTypeModule", "moduleType", "module"], "mappings": "AACA,SAASA,mBAAmB,QAAQ,2BAA0B;AAW9D,OAAO,eAAeC,sBAAsBC,UAAsB;IAChE,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,WAAW,EAAE,GAAGH,UAAU,CAAC,EAAE;IACnD,MAAMI,WAAW,OAAOH,WAAW;IACnC,MAAMI,SAAS,OAAOH,SAAS;IAC/B,MAAMI,gBACJ,OAAOH,gBAAgB,eAAeH,UAAU,CAAC,EAAE,KAAKF;IAE1D,IAAIS,MAAMC;IACV,IAAIC,UAAyCD;IAC7C,IAAIE,WAAWF;IAEf,IAAIJ,UAAU;QACZG,MAAM,MAAMN,MAAM,CAAC,EAAE;QACrBQ,UAAU;QACVC,WAAWT,MAAM,CAAC,EAAE;IACtB,OAAO,IAAII,QAAQ;QACjBE,MAAM,MAAML,IAAI,CAAC,EAAE;QACnBO,UAAU;QACVC,WAAWR,IAAI,CAAC,EAAE;IACpB,OAAO,IAAII,eAAe;QACxBC,MAAM,MAAMJ,WAAW,CAAC,EAAE;QAC1BM,UAAU;QACVC,WAAWP,WAAW,CAAC,EAAE;IAC3B;IAEA,OAAO;QAAEI;QAAKE;QAASC;IAAS;AAClC;AAEA,OAAO,eAAeC,uBACpBX,UAAsB,EACtBY,UAAiE;IAEjE,MAAM,EAAE,CAACA,WAAW,EAAEC,MAAM,EAAE,GAAGb,UAAU,CAAC,EAAE;IAC9C,IAAI,OAAOa,WAAW,aAAa;QACjC,OAAO,MAAMA,MAAM,CAAC,EAAE;IACxB;IACA,OAAOL;AACT"}