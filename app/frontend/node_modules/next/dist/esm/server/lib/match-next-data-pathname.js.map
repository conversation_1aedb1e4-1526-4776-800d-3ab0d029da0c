{"version": 3, "sources": ["../../../src/server/lib/match-next-data-pathname.ts"], "sourcesContent": ["import { getPathMatch } from '../../shared/lib/router/utils/path-match'\n\nconst matcher = getPathMatch('/_next/data/:path*')\n\nexport function matchNextDataPathname(pathname: string | null | undefined) {\n  if (typeof pathname !== 'string') return false\n\n  return matcher(pathname)\n}\n"], "names": ["getPathMatch", "matcher", "matchNextDataPathname", "pathname"], "mappings": "AAAA,SAASA,YAAY,QAAQ,2CAA0C;AAEvE,MAAMC,UAAUD,aAAa;AAE7B,OAAO,SAASE,sBAAsBC,QAAmC;IACvE,IAAI,OAAOA,aAAa,UAAU,OAAO;IAEzC,OAAOF,QAAQE;AACjB"}