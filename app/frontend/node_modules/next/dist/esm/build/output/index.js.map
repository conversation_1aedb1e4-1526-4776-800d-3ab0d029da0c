{"version": 3, "sources": ["../../../src/build/output/index.ts"], "sourcesContent": ["import { bold, red, yellow } from '../../lib/picocolors'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport textTable from 'next/dist/compiled/text-table'\nimport createStore from 'next/dist/compiled/unistore'\nimport formatWebpackMessages from '../../client/components/react-dev-overlay/utils/format-webpack-messages'\nimport { store as consoleStore } from './store'\nimport type { OutputState } from './store'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { COMPILER_NAMES } from '../../shared/lib/constants'\nimport type { CompilerNameValues } from '../../shared/lib/constants'\n\ntype CompilerDiagnostics = {\n  totalModulesCount: number\n  errors: string[] | null\n  warnings: string[] | null\n}\n\ntype WebpackStatus =\n  | { loading: true }\n  | ({ loading: false } & CompilerDiagnostics)\n\ntype AmpStatus = {\n  message: string\n  line: number\n  col: number\n  specUrl: string | null\n  code: string\n}\n\nexport type AmpPageStatus = {\n  [page: string]: { errors: AmpStatus[]; warnings: AmpStatus[] }\n}\n\ntype BuildStatusStore = {\n  client: WebpackStatus\n  server: WebpackStatus\n  edgeServer: WebpackStatus\n  trigger: string | undefined\n  url: string | undefined\n  amp: AmpPageStatus\n}\n\nexport function formatAmpMessages(amp: AmpPageStatus) {\n  let output = bold('Amp Validation') + '\\n\\n'\n  let messages: string[][] = []\n\n  const chalkError = red('error')\n  function ampError(page: string, error: AmpStatus) {\n    messages.push([page, chalkError, error.message, error.specUrl || ''])\n  }\n\n  const chalkWarn = yellow('warn')\n  function ampWarn(page: string, warn: AmpStatus) {\n    messages.push([page, chalkWarn, warn.message, warn.specUrl || ''])\n  }\n\n  for (const page in amp) {\n    let { errors, warnings } = amp[page]\n\n    const devOnlyFilter = (err: AmpStatus) => err.code !== 'DEV_MODE_ONLY'\n    errors = errors.filter(devOnlyFilter)\n    warnings = warnings.filter(devOnlyFilter)\n    if (!(errors.length || warnings.length)) {\n      // Skip page with no non-dev warnings\n      continue\n    }\n\n    if (errors.length) {\n      ampError(page, errors[0])\n      for (let index = 1; index < errors.length; ++index) {\n        ampError('', errors[index])\n      }\n    }\n    if (warnings.length) {\n      ampWarn(errors.length ? '' : page, warnings[0])\n      for (let index = 1; index < warnings.length; ++index) {\n        ampWarn('', warnings[index])\n      }\n    }\n    messages.push(['', '', '', ''])\n  }\n\n  if (!messages.length) {\n    return ''\n  }\n\n  output += textTable(messages, {\n    align: ['l', 'l', 'l', 'l'],\n    stringLength(str: string) {\n      return stripAnsi(str).length\n    },\n  })\n\n  return output\n}\n\nconst buildStore = createStore<BuildStatusStore>({\n  // @ts-expect-error initial value\n  client: {},\n  // @ts-expect-error initial value\n  server: {},\n  // @ts-expect-error initial value\n  edgeServer: {},\n})\nlet buildWasDone = false\nlet clientWasLoading = true\nlet serverWasLoading = true\nlet edgeServerWasLoading = false\n\nbuildStore.subscribe((state) => {\n  const { amp, client, server, edgeServer, trigger, url } = state\n\n  const { appUrl } = consoleStore.getState()\n\n  if (client.loading || server.loading || edgeServer?.loading) {\n    consoleStore.setState(\n      {\n        bootstrap: false,\n        appUrl: appUrl!,\n        // If it takes more than 3 seconds to compile, mark it as loading status\n        loading: true,\n        trigger,\n        url,\n      } as OutputState,\n      true\n    )\n    clientWasLoading = (!buildWasDone && clientWasLoading) || client.loading\n    serverWasLoading = (!buildWasDone && serverWasLoading) || server.loading\n    edgeServerWasLoading =\n      (!buildWasDone && edgeServerWasLoading) || edgeServer.loading\n    buildWasDone = false\n    return\n  }\n\n  buildWasDone = true\n\n  let partialState: Partial<OutputState> = {\n    bootstrap: false,\n    appUrl: appUrl!,\n    loading: false,\n    typeChecking: false,\n    totalModulesCount:\n      (clientWasLoading ? client.totalModulesCount : 0) +\n      (serverWasLoading ? server.totalModulesCount : 0) +\n      (edgeServerWasLoading ? edgeServer?.totalModulesCount || 0 : 0),\n    hasEdgeServer: !!edgeServer,\n  }\n  if (client.errors && clientWasLoading) {\n    // Show only client errors\n    consoleStore.setState(\n      {\n        ...partialState,\n        errors: client.errors,\n        warnings: null,\n      } as OutputState,\n      true\n    )\n  } else if (server.errors && serverWasLoading) {\n    consoleStore.setState(\n      {\n        ...partialState,\n        errors: server.errors,\n        warnings: null,\n      } as OutputState,\n      true\n    )\n  } else if (edgeServer.errors && edgeServerWasLoading) {\n    consoleStore.setState(\n      {\n        ...partialState,\n        errors: edgeServer.errors,\n        warnings: null,\n      } as OutputState,\n      true\n    )\n  } else {\n    // Show warnings from all of them\n    const warnings = [\n      ...(client.warnings || []),\n      ...(server.warnings || []),\n      ...(edgeServer.warnings || []),\n    ].concat(formatAmpMessages(amp) || [])\n\n    consoleStore.setState(\n      {\n        ...partialState,\n        errors: null,\n        warnings: warnings.length === 0 ? null : warnings,\n      } as OutputState,\n      true\n    )\n  }\n})\n\nexport function ampValidation(\n  page: string,\n  errors: AmpStatus[],\n  warnings: AmpStatus[]\n) {\n  const { amp } = buildStore.getState()\n  if (!(errors.length || warnings.length)) {\n    buildStore.setState({\n      amp: Object.keys(amp)\n        .filter((k) => k !== page)\n        .sort()\n        // eslint-disable-next-line no-sequences\n        .reduce((a, c) => ((a[c] = amp[c]), a), {} as AmpPageStatus),\n    })\n    return\n  }\n\n  const newAmp: AmpPageStatus = { ...amp, [page]: { errors, warnings } }\n  buildStore.setState({\n    amp: Object.keys(newAmp)\n      .sort()\n      // eslint-disable-next-line no-sequences\n      .reduce((a, c) => ((a[c] = newAmp[c]), a), {} as AmpPageStatus),\n  })\n}\n\nexport function watchCompilers(\n  client: webpack.Compiler,\n  server: webpack.Compiler,\n  edgeServer: webpack.Compiler\n) {\n  buildStore.setState({\n    client: { loading: true },\n    server: { loading: true },\n    edgeServer: { loading: true },\n    trigger: 'initial',\n    url: undefined,\n  })\n\n  function tapCompiler(\n    key: CompilerNameValues,\n    compiler: webpack.Compiler,\n    onEvent: (status: WebpackStatus) => void\n  ) {\n    compiler.hooks.invalid.tap(`NextJsInvalid-${key}`, () => {\n      onEvent({ loading: true })\n    })\n\n    compiler.hooks.done.tap(`NextJsDone-${key}`, (stats: webpack.Stats) => {\n      buildStore.setState({ amp: {} })\n\n      const { errors, warnings } = formatWebpackMessages(\n        stats.toJson({\n          preset: 'errors-warnings',\n          moduleTrace: true,\n        })\n      )\n\n      const hasErrors = !!errors?.length\n      const hasWarnings = !!warnings?.length\n\n      onEvent({\n        loading: false,\n        totalModulesCount: stats.compilation.modules.size,\n        errors: hasErrors ? errors : null,\n        warnings: hasWarnings ? warnings : null,\n      })\n    })\n  }\n\n  tapCompiler(COMPILER_NAMES.client, client, (status) => {\n    if (\n      !status.loading &&\n      !buildStore.getState().server.loading &&\n      !buildStore.getState().edgeServer.loading &&\n      status.totalModulesCount > 0\n    ) {\n      buildStore.setState({\n        client: status,\n        trigger: undefined,\n        url: undefined,\n      })\n    } else {\n      buildStore.setState({\n        client: status,\n      })\n    }\n  })\n  tapCompiler(COMPILER_NAMES.server, server, (status) => {\n    if (\n      !status.loading &&\n      !buildStore.getState().client.loading &&\n      !buildStore.getState().edgeServer.loading &&\n      status.totalModulesCount > 0\n    ) {\n      buildStore.setState({\n        server: status,\n        trigger: undefined,\n        url: undefined,\n      })\n    } else {\n      buildStore.setState({\n        server: status,\n      })\n    }\n  })\n  tapCompiler(COMPILER_NAMES.edgeServer, edgeServer, (status) => {\n    if (\n      !status.loading &&\n      !buildStore.getState().client.loading &&\n      !buildStore.getState().server.loading &&\n      status.totalModulesCount > 0\n    ) {\n      buildStore.setState({\n        edgeServer: status,\n        trigger: undefined,\n        url: undefined,\n      })\n    } else {\n      buildStore.setState({\n        edgeServer: status,\n      })\n    }\n  })\n}\n\nexport function reportTrigger(trigger: string, url?: string) {\n  buildStore.setState({\n    trigger,\n    url,\n  })\n}\n"], "names": ["bold", "red", "yellow", "stripAnsi", "textTable", "createStore", "formatWebpackMessages", "store", "consoleStore", "COMPILER_NAMES", "formatAmpMessages", "amp", "output", "messages", "chalkError", "ampError", "page", "error", "push", "message", "specUrl", "chalk<PERSON>arn", "ampWarn", "warn", "errors", "warnings", "dev<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "err", "code", "filter", "length", "index", "align", "stringLength", "str", "buildStore", "client", "server", "edgeServer", "buildWasDone", "clientWasLoading", "serverWasLoading", "edgeServerWasLoading", "subscribe", "state", "trigger", "url", "appUrl", "getState", "loading", "setState", "bootstrap", "partialState", "typeChecking", "totalModulesCount", "hasEdgeServer", "concat", "ampValidation", "Object", "keys", "k", "sort", "reduce", "a", "c", "newAmp", "watchCompilers", "undefined", "tapCompiler", "key", "compiler", "onEvent", "hooks", "invalid", "tap", "done", "stats", "to<PERSON><PERSON>", "preset", "moduleTrace", "hasErrors", "hasWarnings", "compilation", "modules", "size", "status", "reportTrigger"], "mappings": "AAAA,SAASA,IAAI,EAAEC,GAAG,EAAEC,MAAM,QAAQ,uBAAsB;AACxD,OAAOC,eAAe,gCAA+B;AACrD,OAAOC,eAAe,gCAA+B;AACrD,OAAOC,iBAAiB,8BAA6B;AACrD,OAAOC,2BAA2B,0EAAyE;AAC3G,SAASC,SAASC,YAAY,QAAQ,UAAS;AAG/C,SAASC,cAAc,QAAQ,6BAA4B;AAkC3D,OAAO,SAASC,kBAAkBC,GAAkB;IAClD,IAAIC,SAASZ,KAAK,oBAAoB;IACtC,IAAIa,WAAuB,EAAE;IAE7B,MAAMC,aAAab,IAAI;IACvB,SAASc,SAASC,IAAY,EAAEC,KAAgB;QAC9CJ,SAASK,IAAI,CAAC;YAACF;YAAMF;YAAYG,MAAME,OAAO;YAAEF,MAAMG,OAAO,IAAI;SAAG;IACtE;IAEA,MAAMC,YAAYnB,OAAO;IACzB,SAASoB,QAAQN,IAAY,EAAEO,IAAe;QAC5CV,SAASK,IAAI,CAAC;YAACF;YAAMK;YAAWE,KAAKJ,OAAO;YAAEI,KAAKH,OAAO,IAAI;SAAG;IACnE;IAEA,IAAK,MAAMJ,QAAQL,IAAK;QACtB,IAAI,EAAEa,MAAM,EAAEC,QAAQ,EAAE,GAAGd,GAAG,CAACK,KAAK;QAEpC,MAAMU,gBAAgB,CAACC,MAAmBA,IAAIC,IAAI,KAAK;QACvDJ,SAASA,OAAOK,MAAM,CAACH;QACvBD,WAAWA,SAASI,MAAM,CAACH;QAC3B,IAAI,CAAEF,CAAAA,OAAOM,MAAM,IAAIL,SAASK,MAAM,AAAD,GAAI;YAEvC;QACF;QAEA,IAAIN,OAAOM,MAAM,EAAE;YACjBf,SAASC,MAAMQ,MAAM,CAAC,EAAE;YACxB,IAAK,IAAIO,QAAQ,GAAGA,QAAQP,OAAOM,MAAM,EAAE,EAAEC,MAAO;gBAClDhB,SAAS,IAAIS,MAAM,CAACO,MAAM;YAC5B;QACF;QACA,IAAIN,SAASK,MAAM,EAAE;YACnBR,QAAQE,OAAOM,MAAM,GAAG,KAAKd,MAAMS,QAAQ,CAAC,EAAE;YAC9C,IAAK,IAAIM,QAAQ,GAAGA,QAAQN,SAASK,MAAM,EAAE,EAAEC,MAAO;gBACpDT,QAAQ,IAAIG,QAAQ,CAACM,MAAM;YAC7B;QACF;QACAlB,SAASK,IAAI,CAAC;YAAC;YAAI;YAAI;YAAI;SAAG;IAChC;IAEA,IAAI,CAACL,SAASiB,MAAM,EAAE;QACpB,OAAO;IACT;IAEAlB,UAAUR,UAAUS,UAAU;QAC5BmB,OAAO;YAAC;YAAK;YAAK;YAAK;SAAI;QAC3BC,cAAaC,GAAW;YACtB,OAAO/B,UAAU+B,KAAKJ,MAAM;QAC9B;IACF;IAEA,OAAOlB;AACT;AAEA,MAAMuB,aAAa9B,YAA8B;IAC/C,iCAAiC;IACjC+B,QAAQ,CAAC;IACT,iCAAiC;IACjCC,QAAQ,CAAC;IACT,iCAAiC;IACjCC,YAAY,CAAC;AACf;AACA,IAAIC,eAAe;AACnB,IAAIC,mBAAmB;AACvB,IAAIC,mBAAmB;AACvB,IAAIC,uBAAuB;AAE3BP,WAAWQ,SAAS,CAAC,CAACC;IACpB,MAAM,EAAEjC,GAAG,EAAEyB,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEO,OAAO,EAAEC,GAAG,EAAE,GAAGF;IAE1D,MAAM,EAAEG,MAAM,EAAE,GAAGvC,aAAawC,QAAQ;IAExC,IAAIZ,OAAOa,OAAO,IAAIZ,OAAOY,OAAO,KAAIX,8BAAAA,WAAYW,OAAO,GAAE;QAC3DzC,aAAa0C,QAAQ,CACnB;YACEC,WAAW;YACXJ,QAAQA;YACR,wEAAwE;YACxEE,SAAS;YACTJ;YACAC;QACF,GACA;QAEFN,mBAAmB,AAAC,CAACD,gBAAgBC,oBAAqBJ,OAAOa,OAAO;QACxER,mBAAmB,AAAC,CAACF,gBAAgBE,oBAAqBJ,OAAOY,OAAO;QACxEP,uBACE,AAAC,CAACH,gBAAgBG,wBAAyBJ,WAAWW,OAAO;QAC/DV,eAAe;QACf;IACF;IAEAA,eAAe;IAEf,IAAIa,eAAqC;QACvCD,WAAW;QACXJ,QAAQA;QACRE,SAAS;QACTI,cAAc;QACdC,mBACE,AAACd,CAAAA,mBAAmBJ,OAAOkB,iBAAiB,GAAG,CAAA,IAC9Cb,CAAAA,mBAAmBJ,OAAOiB,iBAAiB,GAAG,CAAA,IAC9CZ,CAAAA,uBAAuBJ,CAAAA,8BAAAA,WAAYgB,iBAAiB,KAAI,IAAI,CAAA;QAC/DC,eAAe,CAAC,CAACjB;IACnB;IACA,IAAIF,OAAOZ,MAAM,IAAIgB,kBAAkB;QACrC,0BAA0B;QAC1BhC,aAAa0C,QAAQ,CACnB;YACE,GAAGE,YAAY;YACf5B,QAAQY,OAAOZ,MAAM;YACrBC,UAAU;QACZ,GACA;IAEJ,OAAO,IAAIY,OAAOb,MAAM,IAAIiB,kBAAkB;QAC5CjC,aAAa0C,QAAQ,CACnB;YACE,GAAGE,YAAY;YACf5B,QAAQa,OAAOb,MAAM;YACrBC,UAAU;QACZ,GACA;IAEJ,OAAO,IAAIa,WAAWd,MAAM,IAAIkB,sBAAsB;QACpDlC,aAAa0C,QAAQ,CACnB;YACE,GAAGE,YAAY;YACf5B,QAAQc,WAAWd,MAAM;YACzBC,UAAU;QACZ,GACA;IAEJ,OAAO;QACL,iCAAiC;QACjC,MAAMA,WAAW;eACXW,OAAOX,QAAQ,IAAI,EAAE;eACrBY,OAAOZ,QAAQ,IAAI,EAAE;eACrBa,WAAWb,QAAQ,IAAI,EAAE;SAC9B,CAAC+B,MAAM,CAAC9C,kBAAkBC,QAAQ,EAAE;QAErCH,aAAa0C,QAAQ,CACnB;YACE,GAAGE,YAAY;YACf5B,QAAQ;YACRC,UAAUA,SAASK,MAAM,KAAK,IAAI,OAAOL;QAC3C,GACA;IAEJ;AACF;AAEA,OAAO,SAASgC,cACdzC,IAAY,EACZQ,MAAmB,EACnBC,QAAqB;IAErB,MAAM,EAAEd,GAAG,EAAE,GAAGwB,WAAWa,QAAQ;IACnC,IAAI,CAAExB,CAAAA,OAAOM,MAAM,IAAIL,SAASK,MAAM,AAAD,GAAI;QACvCK,WAAWe,QAAQ,CAAC;YAClBvC,KAAK+C,OAAOC,IAAI,CAAChD,KACdkB,MAAM,CAAC,CAAC+B,IAAMA,MAAM5C,MACpB6C,IAAI,EACL,wCAAwC;aACvCC,MAAM,CAAC,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAGrD,GAAG,CAACqD,EAAE,EAAGD,CAAAA,GAAI,CAAC;QAC7C;QACA;IACF;IAEA,MAAME,SAAwB;QAAE,GAAGtD,GAAG;QAAE,CAACK,KAAK,EAAE;YAAEQ;YAAQC;QAAS;IAAE;IACrEU,WAAWe,QAAQ,CAAC;QAClBvC,KAAK+C,OAAOC,IAAI,CAACM,QACdJ,IAAI,EACL,wCAAwC;SACvCC,MAAM,CAAC,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAGC,MAAM,CAACD,EAAE,EAAGD,CAAAA,GAAI,CAAC;IAChD;AACF;AAEA,OAAO,SAASG,eACd9B,MAAwB,EACxBC,MAAwB,EACxBC,UAA4B;IAE5BH,WAAWe,QAAQ,CAAC;QAClBd,QAAQ;YAAEa,SAAS;QAAK;QACxBZ,QAAQ;YAAEY,SAAS;QAAK;QACxBX,YAAY;YAAEW,SAAS;QAAK;QAC5BJ,SAAS;QACTC,KAAKqB;IACP;IAEA,SAASC,YACPC,GAAuB,EACvBC,QAA0B,EAC1BC,OAAwC;QAExCD,SAASE,KAAK,CAACC,OAAO,CAACC,GAAG,CAAC,CAAC,cAAc,EAAEL,KAAK,EAAE;YACjDE,QAAQ;gBAAEtB,SAAS;YAAK;QAC1B;QAEAqB,SAASE,KAAK,CAACG,IAAI,CAACD,GAAG,CAAC,CAAC,WAAW,EAAEL,KAAK,EAAE,CAACO;YAC5CzC,WAAWe,QAAQ,CAAC;gBAAEvC,KAAK,CAAC;YAAE;YAE9B,MAAM,EAAEa,MAAM,EAAEC,QAAQ,EAAE,GAAGnB,sBAC3BsE,MAAMC,MAAM,CAAC;gBACXC,QAAQ;gBACRC,aAAa;YACf;YAGF,MAAMC,YAAY,CAAC,EAACxD,0BAAAA,OAAQM,MAAM;YAClC,MAAMmD,cAAc,CAAC,EAACxD,4BAAAA,SAAUK,MAAM;YAEtCyC,QAAQ;gBACNtB,SAAS;gBACTK,mBAAmBsB,MAAMM,WAAW,CAACC,OAAO,CAACC,IAAI;gBACjD5D,QAAQwD,YAAYxD,SAAS;gBAC7BC,UAAUwD,cAAcxD,WAAW;YACrC;QACF;IACF;IAEA2C,YAAY3D,eAAe2B,MAAM,EAAEA,QAAQ,CAACiD;QAC1C,IACE,CAACA,OAAOpC,OAAO,IACf,CAACd,WAAWa,QAAQ,GAAGX,MAAM,CAACY,OAAO,IACrC,CAACd,WAAWa,QAAQ,GAAGV,UAAU,CAACW,OAAO,IACzCoC,OAAO/B,iBAAiB,GAAG,GAC3B;YACAnB,WAAWe,QAAQ,CAAC;gBAClBd,QAAQiD;gBACRxC,SAASsB;gBACTrB,KAAKqB;YACP;QACF,OAAO;YACLhC,WAAWe,QAAQ,CAAC;gBAClBd,QAAQiD;YACV;QACF;IACF;IACAjB,YAAY3D,eAAe4B,MAAM,EAAEA,QAAQ,CAACgD;QAC1C,IACE,CAACA,OAAOpC,OAAO,IACf,CAACd,WAAWa,QAAQ,GAAGZ,MAAM,CAACa,OAAO,IACrC,CAACd,WAAWa,QAAQ,GAAGV,UAAU,CAACW,OAAO,IACzCoC,OAAO/B,iBAAiB,GAAG,GAC3B;YACAnB,WAAWe,QAAQ,CAAC;gBAClBb,QAAQgD;gBACRxC,SAASsB;gBACTrB,KAAKqB;YACP;QACF,OAAO;YACLhC,WAAWe,QAAQ,CAAC;gBAClBb,QAAQgD;YACV;QACF;IACF;IACAjB,YAAY3D,eAAe6B,UAAU,EAAEA,YAAY,CAAC+C;QAClD,IACE,CAACA,OAAOpC,OAAO,IACf,CAACd,WAAWa,QAAQ,GAAGZ,MAAM,CAACa,OAAO,IACrC,CAACd,WAAWa,QAAQ,GAAGX,MAAM,CAACY,OAAO,IACrCoC,OAAO/B,iBAAiB,GAAG,GAC3B;YACAnB,WAAWe,QAAQ,CAAC;gBAClBZ,YAAY+C;gBACZxC,SAASsB;gBACTrB,KAAKqB;YACP;QACF,OAAO;YACLhC,WAAWe,QAAQ,CAAC;gBAClBZ,YAAY+C;YACd;QACF;IACF;AACF;AAEA,OAAO,SAASC,cAAczC,OAAe,EAAEC,GAAY;IACzDX,WAAWe,QAAQ,CAAC;QAClBL;QACAC;IACF;AACF"}