{"version": 3, "sources": ["../../../../src/build/babel/loader/transform.ts"], "sourcesContent": ["/*\n * Partially adapted from @babel/core (MIT license).\n */\n\nimport traverse from 'next/dist/compiled/babel/traverse'\nimport generate from 'next/dist/compiled/babel/generator'\nimport normalizeFile from 'next/dist/compiled/babel/core-lib-normalize-file'\nimport normalizeOpts from 'next/dist/compiled/babel/core-lib-normalize-opts'\nimport loadBlockHoistPlugin from 'next/dist/compiled/babel/core-lib-block-hoist-plugin'\nimport PluginPass from 'next/dist/compiled/babel/core-lib-plugin-pass'\n\nimport getConfig from './get-config'\nimport { consumeIterator } from './util'\nimport type { Span } from '../../../trace'\nimport type { NextJsLoaderContext } from './types'\n\nfunction getTraversalParams(file: any, pluginPairs: any[]) {\n  const passPairs = []\n  const passes = []\n  const visitors = []\n\n  for (const plugin of pluginPairs.concat(loadBlockHoistPlugin())) {\n    const pass = new PluginPass(file, plugin.key, plugin.options)\n    passPairs.push([plugin, pass])\n    passes.push(pass)\n    visitors.push(plugin.visitor)\n  }\n\n  return { passPairs, passes, visitors }\n}\n\nfunction invokePluginPre(file: any, passPairs: any[]) {\n  for (const [{ pre }, pass] of passPairs) {\n    if (pre) {\n      pre.call(pass, file)\n    }\n  }\n}\n\nfunction invokePluginPost(file: any, passPairs: any[]) {\n  for (const [{ post }, pass] of passPairs) {\n    if (post) {\n      post.call(pass, file)\n    }\n  }\n}\n\nfunction transformAstPass(file: any, pluginPairs: any[], parentSpan: Span) {\n  const { passPairs, passes, visitors } = getTraversalParams(file, pluginPairs)\n\n  invokePluginPre(file, passPairs)\n  const visitor = traverse.visitors.merge(\n    visitors,\n    passes,\n    // @ts-ignore - the exported types are incorrect here\n    file.opts.wrapPluginVisitorMethod\n  )\n\n  parentSpan\n    .traceChild('babel-turbo-traverse')\n    .traceFn(() => traverse(file.ast, visitor, file.scope))\n\n  invokePluginPost(file, passPairs)\n}\n\nfunction transformAst(file: any, babelConfig: any, parentSpan: Span) {\n  for (const pluginPairs of babelConfig.passes) {\n    transformAstPass(file, pluginPairs, parentSpan)\n  }\n}\n\nexport default async function transform(\n  this: NextJsLoaderContext,\n  source: string,\n  inputSourceMap: object | null | undefined,\n  loaderOptions: any,\n  filename: string,\n  target: string,\n  parentSpan: Span\n) {\n  const getConfigSpan = parentSpan.traceChild('babel-turbo-get-config')\n  const babelConfig = await getConfig.call(this, {\n    source,\n    loaderOptions,\n    inputSourceMap,\n    target,\n    filename,\n  })\n  if (!babelConfig) {\n    return { code: source, map: inputSourceMap }\n  }\n  getConfigSpan.stop()\n\n  const normalizeSpan = parentSpan.traceChild('babel-turbo-normalize-file')\n  const file = consumeIterator(\n    normalizeFile(babelConfig.passes, normalizeOpts(babelConfig), source)\n  )\n  normalizeSpan.stop()\n\n  const transformSpan = parentSpan.traceChild('babel-turbo-transform')\n  transformAst(file, babelConfig, transformSpan)\n  transformSpan.stop()\n\n  const generateSpan = parentSpan.traceChild('babel-turbo-generate')\n  const { code, map } = generate(file.ast, file.opts.generatorOpts, file.code)\n  generateSpan.stop()\n\n  return { code, map }\n}\n"], "names": ["traverse", "generate", "normalizeFile", "normalizeOpts", "loadBlockHoistPlugin", "Plug<PERSON><PERSON><PERSON>", "getConfig", "consumeIterator", "getTraversalParams", "file", "pluginPairs", "passPairs", "passes", "visitors", "plugin", "concat", "pass", "key", "options", "push", "visitor", "invokePluginPre", "pre", "call", "invokePluginPost", "post", "transformAstPass", "parentSpan", "merge", "opts", "wrapPluginVisitorMethod", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "ast", "scope", "transformAst", "babelConfig", "transform", "source", "inputSourceMap", "loaderOptions", "filename", "target", "getConfigSpan", "code", "map", "stop", "normalizeSpan", "transformSpan", "generateSpan", "generatorOpts"], "mappings": "AAAA;;CAEC,GAED,OAAOA,cAAc,oCAAmC;AACxD,OAAOC,cAAc,qCAAoC;AACzD,OAAOC,mBAAmB,mDAAkD;AAC5E,OAAOC,mBAAmB,mDAAkD;AAC5E,OAAOC,0BAA0B,uDAAsD;AACvF,OAAOC,gBAAgB,gDAA+C;AAEtE,OAAOC,eAAe,eAAc;AACpC,SAASC,eAAe,QAAQ,SAAQ;AAIxC,SAASC,mBAAmBC,IAAS,EAAEC,WAAkB;IACvD,MAAMC,YAAY,EAAE;IACpB,MAAMC,SAAS,EAAE;IACjB,MAAMC,WAAW,EAAE;IAEnB,KAAK,MAAMC,UAAUJ,YAAYK,MAAM,CAACX,wBAAyB;QAC/D,MAAMY,OAAO,IAAIX,WAAWI,MAAMK,OAAOG,GAAG,EAAEH,OAAOI,OAAO;QAC5DP,UAAUQ,IAAI,CAAC;YAACL;YAAQE;SAAK;QAC7BJ,OAAOO,IAAI,CAACH;QACZH,SAASM,IAAI,CAACL,OAAOM,OAAO;IAC9B;IAEA,OAAO;QAAET;QAAWC;QAAQC;IAAS;AACvC;AAEA,SAASQ,gBAAgBZ,IAAS,EAAEE,SAAgB;IAClD,KAAK,MAAM,CAAC,EAAEW,GAAG,EAAE,EAAEN,KAAK,IAAIL,UAAW;QACvC,IAAIW,KAAK;YACPA,IAAIC,IAAI,CAACP,MAAMP;QACjB;IACF;AACF;AAEA,SAASe,iBAAiBf,IAAS,EAAEE,SAAgB;IACnD,KAAK,MAAM,CAAC,EAAEc,IAAI,EAAE,EAAET,KAAK,IAAIL,UAAW;QACxC,IAAIc,MAAM;YACRA,KAAKF,IAAI,CAACP,MAAMP;QAClB;IACF;AACF;AAEA,SAASiB,iBAAiBjB,IAAS,EAAEC,WAAkB,EAAEiB,UAAgB;IACvE,MAAM,EAAEhB,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAE,GAAGL,mBAAmBC,MAAMC;IAEjEW,gBAAgBZ,MAAME;IACtB,MAAMS,UAAUpB,SAASa,QAAQ,CAACe,KAAK,CACrCf,UACAD,QACA,qDAAqD;IACrDH,KAAKoB,IAAI,CAACC,uBAAuB;IAGnCH,WACGI,UAAU,CAAC,wBACXC,OAAO,CAAC,IAAMhC,SAASS,KAAKwB,GAAG,EAAEb,SAASX,KAAKyB,KAAK;IAEvDV,iBAAiBf,MAAME;AACzB;AAEA,SAASwB,aAAa1B,IAAS,EAAE2B,WAAgB,EAAET,UAAgB;IACjE,KAAK,MAAMjB,eAAe0B,YAAYxB,MAAM,CAAE;QAC5Cc,iBAAiBjB,MAAMC,aAAaiB;IACtC;AACF;AAEA,eAAe,eAAeU,UAE5BC,MAAc,EACdC,cAAyC,EACzCC,aAAkB,EAClBC,QAAgB,EAChBC,MAAc,EACdf,UAAgB;IAEhB,MAAMgB,gBAAgBhB,WAAWI,UAAU,CAAC;IAC5C,MAAMK,cAAc,MAAM9B,UAAUiB,IAAI,CAAC,IAAI,EAAE;QAC7Ce;QACAE;QACAD;QACAG;QACAD;IACF;IACA,IAAI,CAACL,aAAa;QAChB,OAAO;YAAEQ,MAAMN;YAAQO,KAAKN;QAAe;IAC7C;IACAI,cAAcG,IAAI;IAElB,MAAMC,gBAAgBpB,WAAWI,UAAU,CAAC;IAC5C,MAAMtB,OAAOF,gBACXL,cAAckC,YAAYxB,MAAM,EAAET,cAAciC,cAAcE;IAEhES,cAAcD,IAAI;IAElB,MAAME,gBAAgBrB,WAAWI,UAAU,CAAC;IAC5CI,aAAa1B,MAAM2B,aAAaY;IAChCA,cAAcF,IAAI;IAElB,MAAMG,eAAetB,WAAWI,UAAU,CAAC;IAC3C,MAAM,EAAEa,IAAI,EAAEC,GAAG,EAAE,GAAG5C,SAASQ,KAAKwB,GAAG,EAAExB,KAAKoB,IAAI,CAACqB,aAAa,EAAEzC,KAAKmC,IAAI;IAC3EK,aAAaH,IAAI;IAEjB,OAAO;QAAEF;QAAMC;IAAI;AACrB"}